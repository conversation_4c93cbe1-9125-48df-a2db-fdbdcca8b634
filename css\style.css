/* ===============================================
   VARIABLES & BASE STYLES
   =============================================== */

@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

:root {
    /* Brand Colors */
    --primary: #4e73df;
    --primary-dark: #2e59d9;
    --secondary: #858796;
    --success: #1cc88a;
    --info: #36b9cc;
    --warning: #f6c23e;
    --danger: #e74a3b;
    --light: #f8f9fc;
    --dark: #5a5c69;
    
    /* Grayscale */
    --gray-100: #f8f9fc;
    --gray-200: #eaecf4;
    --gray-300: #dddfeb;
    --gray-400: #d1d3e2;
    --gray-500: #b7b9cc;
    --gray-600: #858796;
    --gray-700: #6e707e;
    --gray-800: #5a5c69;
    --gray-900: #3a3b45;
    
    /* Layout */
    --sidebar-width: 250px;
    --topbar-height: 60px;
    --transition-speed: 0.3s;
    --border-radius: 0.35rem;
    --box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    position: relative;
    min-height: 100%;
}

body {
    font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    font-size: 0.9rem;
    font-weight: 400;
    line-height: 1.5;
    color: #4a4b4d;
    background-color: var(--gray-100);
    -webkit-text-size-adjust: 100%;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    overflow-x: hidden;
}

a {
    color: var(--primary);
    text-decoration: none;
    background-color: transparent;
    transition: color 0.15s ease-in-out;
}

a:hover {
    color: var(--primary-dark);
    text-decoration: none;
}

h1, h2, h3, h4, h5, h6,
.h1, .h2, .h3, .h4, .h5, .h6 {
    margin-bottom: 0.5rem;
    font-weight: 600;
    line-height: 1.2;
    color: var(--gray-900);
}

h1, .h1 { font-size: 2rem; }
h2, .h2 { font-size: 1.75rem; }
h3, .h3 { font-size: 1.5rem; }
h4, .h4 { font-size: 1.25rem; }
h5, .h5 { font-size: 1.1rem; }
h6, .h6 { font-size: 1rem; }

p {
    margin-top: 0;
    margin-bottom: 1rem;
}

/* ===============================================
   LAYOUT
   =============================================== */

.wrapper {
    display: flex;
    width: 100%;
    align-items: stretch;
    min-height: 100vh;
}

/* ===============================================
   SIDEBAR
   =============================================== */

#sidebar {
    width: var(--sidebar-width);
    background: #fff;
    color: #4a4b4d;
    transition: all var(--transition-speed) ease;
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 1000;
    box-shadow: 0 0 15px 0 rgba(0, 0, 0, 0.05);
    overflow-y: auto;
}

#sidebar .sidebar-header {
    padding: 1.5rem 1.5rem 0.5rem;
    background: var(--primary);
    color: #fff;
    min-height: 80px;
    display: flex;
    align-items: center;
}

#sidebar .sidebar-header h3 {
    color: #fff;
    margin: 0;
    font-size: 1.25rem;
    font-weight: 600;
}

#sidebar .sidebar-header .logo {
    height: 2.5rem;
    margin-right: 0.5rem;
}

#sidebar ul.components {
    padding: 1rem 0;
}

#sidebar ul li a {
    padding: 0.8rem 1.5rem;
    font-size: 0.9rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    color: var(--gray-700);
    text-decoration: none;
    transition: all 0.2s ease;
    border-left: 3px solid transparent;
}

#sidebar ul li a i {
    width: 1.5rem;
    margin-right: 0.5rem;
    text-align: center;
    font-size: 1.1rem;
    color: var(--gray-500);
}

#sidebar ul li a:hover {
    background-color: var(--gray-100);
    color: var(--primary);
    border-left: 3px solid var(--primary);
}

#sidebar ul li a:hover i {
    color: var(--primary);
}

#sidebar ul li.active > a {
    background-color: rgba(78, 115, 223, 0.1);
    color: var(--primary);
    font-weight: 600;
    border-left: 3px solid var(--primary);
}

#sidebar ul li.active > a i {
    color: var(--primary);
}

#sidebar .dropdown-toggle::after {
    display: block;
    position: absolute;
    top: 50%;
    right: 1.5rem;
    transform: translateY(-50%);
    transition: transform 0.2s ease;
}

#sidebar .collapsed .dropdown-toggle::after {
    transform: translateY(-50%) rotate(-90deg);
}

#sidebar ul ul a {
    font-size: 0.85rem !important;
    padding-left: 3.5rem !important;
    background: transparent;
    font-weight: 400;
}

#sidebar ul ul a:hover {
    background-color: var(--gray-50);
}

#sidebar.collapsed {
    margin-left: calc(-1 * var(--sidebar-width));
}

/* Sidebar Toggle Button */
#sidebarCollapse {
    cursor: pointer;
    padding: 0.5rem;
    background: transparent;
    border: none;
    color: var(--gray-600);
    font-size: 1.25rem;
    transition: all 0.2s ease;
}

#sidebarCollapse:hover {
    color: var(--primary);
}

#sidebarCollapse:focus {
    outline: none;
    box-shadow: none;
}

/* ===============================================
   MAIN CONTENT
   =============================================== */

#content {
    width: calc(100% - var(--sidebar-width));
    min-height: 100vh;
    margin-left: var(--sidebar-width);
    transition: all var(--transition-speed) ease;
    background-color: var(--gray-100);
    padding: 1.5rem;
    padding-top: calc(var(--topbar-height) + 1rem);
}

#content.active {
    width: 100%;
    margin-left: 0;
}

/* Content Header */
.content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--gray-200);
}

.content-header h1 {
    margin: 0;
    font-size: 1.75rem;
    font-weight: 600;
    color: var(--gray-800);
}

/* Breadcrumb */
.breadcrumb {
    background: transparent;
    padding: 0.5rem 0;
    margin-bottom: 0;
    font-size: 0.9rem;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: '›';
    padding: 0 0.5rem;
    color: var(--gray-600);
}

.breadcrumb-item a {
    color: var(--gray-600);
}

.breadcrumb-item.active {
    color: var(--primary);
    font-weight: 500;
}

/* ===============================================
   TOP NAVIGATION
   =============================================== */

.topbar {
    height: var(--topbar-height);
    background: #fff;
    padding: 0 1.5rem;
    position: fixed;
    top: 0;
    right: 0;
    left: var(--sidebar-width);
    z-index: 999;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    transition: all var(--transition-speed) ease;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.topbar.active {
    left: 0;
}

.topbar-nav {
    display: flex;
    align-items: center;
    height: 100%;
}

.topbar-divider {
    width: 1px;
    height: 2.5rem;
    background-color: var(--gray-200);
    margin: 0 1rem;
}

/* Topbar Search */
.topbar-search {
    position: relative;
    max-width: 25rem;
    margin-right: 1rem;
}

.topbar-search .form-control {
    padding-left: 2.5rem;
    height: calc(1.5em + 0.75rem + 2px);
    border-radius: 2rem;
    border: 1px solid var(--gray-300);
    background-color: var(--gray-100);
    transition: all 0.2s ease;
}

.topbar-search .form-control:focus {
    background-color: #fff;
    border-color: var(--primary);
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

.topbar-search .search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--gray-500);
    z-index: 10;
}

/* Topbar Nav */
.navbar-nav {
    display: flex;
    align-items: center;
    margin: 0;
    padding: 0;
    list-style: none;
}

.nav-item {
    position: relative;
}

.nav-link {
    display: flex;
    align-items: center;
    height: var(--topbar-height);
    padding: 0 0.75rem;
    color: var(--gray-600);
    text-decoration: none;
    transition: all 0.2s ease;
    position: relative;
}

.nav-link:hover {
    color: var(--primary);
}

.nav-link .badge-counter {
    position: absolute;
    top: 0.5rem;
    right: 0.25rem;
    font-size: 0.6rem;
    font-weight: 600;
    min-width: 1.25rem;
    height: 1.25rem;
    line-height: 1.25rem;
    padding: 0 0.35rem;
    border-radius: 10rem;
}

/* User Dropdown */
.dropdown {
    position: relative;
}

.dropdown-toggle::after {
    display: inline-block;
    margin-left: 0.255em;
    vertical-align: 0.255em;
    content: '';
    border-top: 0.3em solid;
    border-right: 0.3em solid transparent;
    border-bottom: 0;
    border-left: 0.3em solid transparent;
    transition: transform 0.2s ease;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    z-index: 1000;
    display: none;
    float: left;
    min-width: 10rem;
    padding: 0.5rem 0;
    margin: 0.125rem 0 0;
    font-size: 0.85rem;
    color: #212529;
    text-align: left;
    list-style: none;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid rgba(0, 0, 0, 0.15);
    border-radius: 0.35rem;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.dropdown-menu.show {
    display: block;
}

.dropdown-item {
    display: block;
    width: 100%;
    padding: 0.5rem 1.5rem;
    clear: both;
    font-weight: 400;
    color: #212529;
    text-align: inherit;
    white-space: nowrap;
    background-color: transparent;
    border: 0;
    transition: all 0.2s ease;
}

.dropdown-item:hover, .dropdown-item:focus {
    color: #16181b;
    text-decoration: none;
    background-color: #f8f9fa;
}

.dropdown-item.active, .dropdown-item:active {
    color: #fff;
    text-decoration: none;
    background-color: var(--primary);
}

.dropdown-divider {
    height: 0;
    margin: 0.5rem 0;
    overflow: hidden;
    border-top: 1px solid #e3e6f0;
}

/* User Profile */
.topbar .dropdown-list-image {
    position: relative;
    width: 2.5rem;
    height: 2.5rem;
    margin-right: 0.5rem;
}

.topbar .dropdown-list-image img {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    object-fit: cover;
}

.topbar .dropdown-list-image .status-indicator {
    background-color: #1cc88a;
    height: 0.75rem;
    width: 0.75rem;
    border-radius: 100%;
    position: absolute;
    bottom: 0;
    right: 0;
    border: 2px solid #fff;
}

.topbar .nav-item .nav-link .img-profile {
    height: 2rem;
    width: 2rem;
}

/* Topbar Dropdown Animation */
.topbar .dropdown-menu {
    animation: fadeIn 0.2s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Card Styles */
.card {
    position: relative;
    display: flex;
    flex-direction: column;
    min-width: 0;
    word-wrap: break-word;
    background-color: #fff;
    background-clip: border-box;
    border: 1px solid #e3e6f0;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    margin-bottom: 1.5rem;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1.5rem 0.5rem rgba(58, 59, 69, 0.1);
}

.card-header {
    padding: 1rem 1.25rem;
    margin-bottom: 0;
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
    border-top-left-radius: calc(0.35rem - 1px);
    border-top-right-radius: calc(0.35rem - 1px);
}

.card-header h6 {
    color: var(--gray-800);
    font-weight: 600;
    margin: 0;
    text-transform: uppercase;
    font-size: 0.8rem;
    letter-spacing: 0.05em;
}

.card-body {
    flex: 1 1 auto;
    padding: 1.25rem;
}

.card-footer {
    padding: 0.75rem 1.25rem;
    background-color: #f8f9fc;
    border-top: 1px solid #e3e6f0;
    border-bottom-right-radius: calc(0.35rem - 1px);
    border-bottom-left-radius: calc(0.35rem - 1px);
}

/* Card Stats */
.card-stats {
    border-left: 0.25rem solid var(--primary);
}

.card-stats .card-body {
    padding: 1rem 1.5rem;
}

.card-stats .card-title {
    text-transform: uppercase;
    font-size: 0.7rem;
    font-weight: 600;
    letter-spacing: 0.1em;
    margin-bottom: 0.5rem;
    color: var(--gray-600);
}

.card-stats .card-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: 0.25rem;
    line-height: 1.2;
}

.card-stats .card-icon {
    font-size: 1.5rem;
    color: var(--primary);
    opacity: 0.3;
    position: absolute;
    right: 1.5rem;
    top: 1.5rem;
}

/* Card Themes */
.card-primary {
    border-left-color: var(--primary);
}

.card-primary .card-icon {
    color: var(--primary);
}

.card-success {
    border-left-color: var(--success);
}

.card-success .card-icon {
    color: var(--success);
}

.card-info {
    border-left-color: var(--info);
}

.card-info .card-icon {
    color: var(--info);
}

.card-warning {
    border-left-color: var(--warning);
}

.card-warning .card-icon {
    color: var(--warning);
}

.card-danger {
    border-left-color: var(--danger);
}

.card-danger .card-icon {
    color: var(--danger);
}

/* Chart Card */
.card-chart {
    overflow: hidden;
}

.card-chart .card-header {
    position: relative;
    z-index: 1;
    background-color: transparent;
    border: none;
    padding-bottom: 0;
}

.card-chart .card-body {
    padding: 0;
}

.card-chart .chart-area {
    height: 10rem;
    width: 100%;
    position: relative;
}

/* Progress Card */
.progress-sm {
    height: 0.5rem;
}

.progress {
    display: flex;
    height: 1rem;
    overflow: hidden;
    font-size: 0.75rem;
    background-color: #eaecf4;
    border-radius: 0.35rem;
    margin-bottom: 1rem;
}

.progress-bar {
    display: flex;
    flex-direction: column;
    justify-content: center;
    color: #fff;
    text-align: center;
    white-space: nowrap;
    background-color: var(--primary);
    transition: width 0.6s ease;
}

/* ===============================================
   BUTTONS
   =============================================== */

.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-weight: 500;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    user-select: none;
    border: 1px solid transparent;
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
    line-height: 1.5;
    border-radius: var(--border-radius);
    transition: all 0.15s ease-in-out;
    cursor: pointer;
}

.btn i {
    margin-right: 0.5rem;
    font-size: 1rem;
    line-height: 1;
}

.btn-sm {
    padding: 0.25rem 0.75rem;
    font-size: 0.8rem;
}

.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1.1rem;
}

.btn-primary {
    color: #fff;
    background-color: var(--primary);
    border-color: var(--primary);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
}

.btn-outline-primary {
    color: var(--primary);
    border-color: var(--primary);
    background-color: transparent;
}

.btn-outline-primary:hover {
    color: #fff;
    background-color: var(--primary);
    border-color: var(--primary);
}

.btn-success {
    color: #fff;
    background-color: var(--success);
    border-color: var(--success);
}

.btn-success:hover {
    background-color: #169b6b;
    border-color: #158863;
}

.btn-info {
    color: #fff;
    background-color: var(--info);
    border-color: var(--info);
}

.btn-info:hover {
    background-color: #2c9faf;
    border-color: #2a96a5;
}

.btn-warning {
    color: #212529;
    background-color: var(--warning);
    border-color: var(--warning);
}

.btn-warning:hover {
    background-color: #e0a800;
    border-color: #d39e00;
}

.btn-danger {
    color: #fff;
    background-color: var(--danger);
    border-color: var(--danger);
}

.btn-danger:hover {
    background-color: #e02d1b;
    border-color: #d52a1a;
}

.btn-link {
    font-weight: 400;
    color: var(--primary);
    text-decoration: none;
    background-color: transparent;
    border: none;
    padding: 0;
}

.btn-link:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

.btn-icon {
    width: 2.5rem;
    height: 2.5rem;
    padding: 0;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.btn-icon i {
    margin: 0;
}

/* Button Group */
.btn-group {
    position: relative;
    display: inline-flex;
    vertical-align: middle;
}

.btn-group > .btn:first-child {
    margin-left: 0;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.btn-group > .btn:not(:first-child) {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    margin-left: -1px;
}

/* ===============================================
   FORMS
   =============================================== */

.form-control {
    display: block;
    width: 100%;
    padding: 0.5rem 0.75rem;
    font-size: 0.9rem;
    font-weight: 400;
    line-height: 1.5;
    color: #6e707e;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #d1d3e2;
    border-radius: var(--border-radius);
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
    color: #6e707e;
    background-color: #fff;
    border-color: #bac8f3;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

.form-label {
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--gray-700);
}

.form-text {
    display: block;
    margin-top: 0.25rem;
    font-size: 0.8rem;
    color: var(--gray-600);
}

/* Form Groups */
.form-group {
    margin-bottom: 1.25rem;
}

/* Form Validation */
.is-valid ~ .valid-feedback,
.is-valid ~ .valid-tooltip,
.was-validated :valid ~ .valid-feedback,
.was-validated :valid ~ .valid-tooltip {
    display: block;
}

.is-invalid ~ .invalid-feedback,
.is-invalid ~ .invalid-tooltip,
.was-validated :invalid ~ .invalid-feedback,
.was-validated :invalid ~ .invalid-tooltip {
    display: block;
}

.valid-feedback {
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.8rem;
    color: #1cc88a;
}

.invalid-feedback {
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.8rem;
    color: #e74a3b;
}

/* Form Check */
.form-check {
    display: block;
    min-height: 1.5rem;
    padding-left: 1.5em;
    margin-bottom: 0.125rem;
}

.form-check-input {
    float: left;
    margin-left: -1.5em;
    width: 1em;
    height: 1em;
    margin-top: 0.25em;
    vertical-align: top;
    background-color: #fff;
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
    border: 1px solid #d1d3e2;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
    transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-check-input:checked {
    background-color: var(--primary);
    border-color: var(--primary);
}

.form-check-input:focus {
    border-color: #bac8f3;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

/* Form Select */
.form-select {
    display: block;
    width: 100%;
    padding: 0.5rem 2.25rem 0.5rem 0.75rem;
    font-size: 0.9rem;
    font-weight: 400;
    line-height: 1.5;
    color: #6e707e;
    background-color: #fff;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23d1d3e2' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 16px 12px;
    border: 1px solid #d1d3e2;
    border-radius: var(--border-radius);
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

.form-select:focus {
    border-color: #bac8f3;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

/* Form Range */
.form-range {
    width: 100%;
    height: 1.5rem;
    padding: 0;
    background-color: transparent;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

.form-range:focus {
    outline: 0;
}

.form-range::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 1rem;
    height: 1rem;
    margin-top: -0.25rem;
    background-color: var(--primary);
    border: 0;
    border-radius: 1rem;
    transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-range::-webkit-slider-thumb:active {
    background-color: #e0e6ff;
}

.form-range::-webkit-slider-runnable-track {
    width: 100%;
    height: 0.5rem;
    color: transparent;
    cursor: pointer;
    background-color: #eaecf4;
    border-color: transparent;
    border-radius: 1rem;
}

/* ===============================================
   TABLES
   =============================================== */

.table {
    width: 100%;
    margin-bottom: 1rem;
    color: var(--gray-700);
    vertical-align: top;
    border-color: #e3e6f0;
    border-collapse: collapse;
}

.table > :not(caption) > * > * {
    padding: 1rem 1rem;
    background-color: var(--bs-table-bg);
    border-bottom-width: 1px;
    box-shadow: inset 0 0 0 9999px var(--bs-table-accent-bg);
}

.table > thead {
    vertical-align: bottom;
}

.table > tbody {
    vertical-align: inherit;
}

.table > thead th {
    color: var(--gray-600);
    background-color: #f8f9fc;
    border-bottom-width: 1px;
    text-transform: uppercase;
    font-size: 0.7rem;
    font-weight: 600;
    letter-spacing: 0.1em;
    padding: 0.75rem 1rem;
}

.table > :not(:first-child) {
    border-top: 2px solid #e3e6f0;
}

.table > tbody > tr > td {
    padding: 1rem;
    vertical-align: middle;
    border-top: 1px solid #e3e6f0;
}

/* Table Striped */
.table-striped > tbody > tr:nth-of-type(odd) > * {
    --bs-table-accent-bg: rgba(0, 0, 0, 0.02);
    color: var(--bs-table-striped-color);
}

/* Table Hover */
.table-hover > tbody > tr:hover > * {
    --bs-table-accent-bg: rgba(0, 0, 0, 0.03);
    color: var(--bs-table-hover-color);
}

/* Table Bordered */
.table-bordered > :not(caption) > * {
    border-width: 1px 0;
}

.table-bordered > :not(caption) > * > * {
    border-width: 0 1px;
}

/* Table Responsive */
.table-responsive {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

/* Table Dark */
.table-dark {
    --bs-table-bg: #212529;
    --bs-table-striped-bg: #2c3034;
    --bs-table-striped-color: #fff;
    --bs-table-active-bg: #373b3e;
    --bs-table-active-color: #fff;
    --bs-table-hover-bg: #323539;
    --bs-table-hover-color: #fff;
    color: #fff;
    border-color: #373b3e;
}

/* Table Actions */
.table-actions {
    white-space: nowrap;
    width: 1%;
}

.table-actions .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
    line-height: 1.5;
    border-radius: 0.2rem;
}

/* Table Status */
.status-badge {
    display: inline-block;
    padding: 0.35em 0.65em;
    font-size: 0.75em;
    font-weight: 700;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 0.25rem;
}

.status-active {
    color: #0f5132;
    background-color: #d1e7dd;
}

.status-inactive {
    color: #842029;
    background-color: #f8d7da;
}

.status-pending {
    color: #664d03;
    background-color: #fff3cd;
}

/* Table Avatar */
.table-avatar {
    display: inline-flex;
    align-items: center;
}

.table-avatar img {
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.table-avatar .avatar-info {
    line-height: 1.2;
}

.table-avatar .avatar-name {
    font-weight: 600;
    color: var(--gray-900);
    display: block;
}

.table-avatar .avatar-email {
    font-size: 0.75rem;
    color: var(--gray-600);
    display: block;
}

/* Responsive Styles */
@media (max-width: 768px) {
    #sidebar {
        margin-left: -250px;
    }
    #sidebar.active {
        margin-left: 0;
    }
    #content {
        width: 100%;
    }
    #content.active {
        width: calc(100% - 250px);
    }
}

/* Animation */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.card, .table {
    animation: fadeIn 0.5s ease-out;
}

/* Quick Actions */
.quick-actions {
    position: fixed;
    bottom: 30px;
    right: 30px;
    z-index: 1000;
}

.btn-floating {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    font-size: 1.5rem;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.quick-actions-menu {
    position: absolute;
    bottom: 70px;
    right: 0;
    display: none;
    flex-direction: column;
    gap: 10px;
    min-width: 200px;
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.3s ease;
}

.quick-actions:hover .quick-actions-menu {
    display: flex;
    opacity: 1;
    transform: translateY(0);
}

.quick-actions-menu button {
    white-space: nowrap;
    text-align: left;
    opacity: 0;
    transform: translateX(20px);
    transition: all 0.3s ease;
}

.quick-actions:hover .quick-actions-menu button {
    opacity: 1;
    transform: translateX(0);
}

.quick-actions-menu button:nth-child(1) { transition-delay: 0.1s; }
.quick-actions-menu button:nth-child(2) { transition-delay: 0.2s; }
.quick-actions-menu button:nth-child(3) { transition-delay: 0.3s; }

/* Modal Styles */
.modal-content {
    border: none;
    border-radius: 10px;
    overflow: hidden;
}

.modal-header {
    padding: 1.2rem 1.5rem;
}

.modal-body {
    padding: 1.5rem;
}

/* Form Styles */
.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(52, 152, 219, 0.25);
}

/* Alert Styles */
.alert {
    border: none;
    border-radius: 8px;
    padding: 1rem 1.25rem;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #555;
}
