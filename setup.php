<?php
/**
 * Setup script for Microfinance Management System
 * This script helps initialize the database and create sample data
 */

require_once 'config/database.php';

// Check if setup has already been run
function isSetupComplete() {
    try {
        $db = Database::getInstance()->getConnection();
        $stmt = $db->query("SELECT COUNT(*) FROM users WHERE role = 'admin'");
        return $stmt->fetchColumn() > 0;
    } catch (Exception $e) {
        return false;
    }
}

// Create default admin user
function createAdminUser($username, $password, $email, $fullName) {
    try {
        $db = Database::getInstance()->getConnection();
        
        // Check if admin already exists
        $stmt = $db->prepare("SELECT COUNT(*) FROM users WHERE username = ? OR email = ?");
        $stmt->execute([$username, $email]);
        
        if ($stmt->fetchColumn() > 0) {
            return ['success' => false, 'message' => 'Admin user already exists'];
        }
        
        // Create admin user
        $passwordHash = password_hash($password, PASSWORD_BCRYPT);
        $stmt = $db->prepare("
            INSERT INTO users (username, password_hash, full_name, email, role, status) 
            VALUES (?, ?, ?, ?, 'admin', 'active')
        ");
        
        $stmt->execute([$username, $passwordHash, $fullName, $email]);
        
        return ['success' => true, 'message' => 'Admin user created successfully'];
        
    } catch (Exception $e) {
        return ['success' => false, 'message' => 'Error creating admin user: ' . $e->getMessage()];
    }
}

// Handle form submission
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['create_admin'])) {
        $result = createAdminUser(
            $_POST['username'],
            $_POST['password'],
            $_POST['email'],
            $_POST['full_name']
        );
        
        $message = $result['message'];
        $messageType = $result['success'] ? 'success' : 'danger';
    }
    
    if (isset($_POST['load_sample_data'])) {
        try {
            $db = Database::getInstance()->getConnection();
            $sampleDataSql = file_get_contents('config/sample_data.sql');
            
            // Split and execute SQL statements
            $statements = explode(';', $sampleDataSql);
            foreach ($statements as $statement) {
                $statement = trim($statement);
                if (!empty($statement) && !preg_match('/^(--|USE)/i', $statement)) {
                    $db->exec($statement);
                }
            }
            
            $message = 'Sample data loaded successfully';
            $messageType = 'success';
            
        } catch (Exception $e) {
            $message = 'Error loading sample data: ' . $e->getMessage();
            $messageType = 'danger';
        }
    }
}

$setupComplete = isSetupComplete();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Setup - Microfinance Management System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-cog me-2"></i>
                            Microfinance System Setup
                        </h4>
                    </div>
                    <div class="card-body">
                        <?php if ($message): ?>
                            <div class="alert alert-<?php echo $messageType; ?> alert-dismissible fade show">
                                <?php echo htmlspecialchars($message); ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        <?php endif; ?>

                        <?php if ($setupComplete): ?>
                            <div class="alert alert-success">
                                <i class="fas fa-check-circle me-2"></i>
                                Setup is complete! You can now access the system.
                            </div>
                            <div class="text-center">
                                <a href="login.html" class="btn btn-primary">
                                    <i class="fas fa-sign-in-alt me-2"></i>
                                    Go to Login
                                </a>
                            </div>
                        <?php else: ?>
                            <h5>Create Admin User</h5>
                            <p class="text-muted">Create the first administrator account for your system.</p>
                            
                            <form method="POST">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="username" class="form-label">Username</label>
                                            <input type="text" class="form-control" id="username" name="username" required>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label for="email" class="form-label">Email</label>
                                            <input type="email" class="form-control" id="email" name="email" required>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="full_name" class="form-label">Full Name</label>
                                    <input type="text" class="form-control" id="full_name" name="full_name" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="password" class="form-label">Password</label>
                                    <input type="password" class="form-control" id="password" name="password" required minlength="6">
                                    <div class="form-text">Password must be at least 6 characters long.</div>
                                </div>
                                
                                <button type="submit" name="create_admin" class="btn btn-primary">
                                    <i class="fas fa-user-plus me-2"></i>
                                    Create Admin User
                                </button>
                            </form>
                        <?php endif; ?>

                        <hr>

                        <h5>Load Sample Data</h5>
                        <p class="text-muted">Load sample clients, loans, and other test data for demonstration purposes.</p>
                        
                        <form method="POST" onsubmit="return confirm('This will add sample data to your database. Continue?')">
                            <button type="submit" name="load_sample_data" class="btn btn-outline-secondary">
                                <i class="fas fa-database me-2"></i>
                                Load Sample Data
                            </button>
                        </form>

                        <hr>

                        <div class="alert alert-info">
                            <h6><i class="fas fa-info-circle me-2"></i>Next Steps:</h6>
                            <ol class="mb-0">
                                <li>Create an admin user above</li>
                                <li>Optionally load sample data</li>
                                <li>Access the system via <a href="login.html">login page</a></li>
                                <li>Configure your loan products and settings</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
