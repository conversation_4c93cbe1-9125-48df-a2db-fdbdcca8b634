class ReportService {
    constructor() {
        this.apiService = apiService;
    }

    // Generate a report
    async generateReport(reportType, filters = {}) {
        try {
            const data = await this.apiService.generateReport(reportType, filters);
            return { success: true, data };
        } catch (error) {
            console.error(`Failed to generate ${reportType} report:`, error);
            return { 
                success: false, 
                error: error.message || `Failed to generate ${reportType} report. Please try again.`
            };
        }
    }

    // Get available report types
    getReportTypes() {
        return [
            {
                id: 'loan_portfolio',
                name: 'Loan <PERSON>folio',
                description: 'Summary of all loans with status, amounts, and performance metrics',
                icon: 'fa-file-invoice-dollar',
                categories: ['loans', 'portfolio']
            },
            {
                id: 'loan_performance',
                name: 'Loan Performance',
                description: 'Detailed analysis of loan performance and repayment rates',
                icon: 'fa-chart-line',
                categories: ['loans', 'performance']
            },
            {
                id: 'client_listing',
                name: 'Client Listing',
                description: 'Comprehensive list of all clients with contact information',
                icon: 'fa-users',
                categories: ['clients', 'listing']
            },
            {
                id: 'client_activity',
                name: 'Client Activity',
                description: 'Detailed client transaction and interaction history',
                icon: 'fa-user-clock',
                categories: ['clients', 'activity']
            },
            {
                id: 'collection_sheet',
                name: 'Collection Sheet',
                description: 'Daily, weekly, or monthly collection sheet for field officers',
                icon: 'fa-clipboard-list',
                categories: ['collections', 'field']
            },
            {
                id: 'arrears_report',
                name: 'Arrears Report',
                description: 'List of all loans with overdue payments',
                icon: 'fa-exclamation-triangle',
                categories: ['loans', 'collections', 'risk']
            },
            {
                id: 'financial_statement',
                name: 'Financial Statement',
                description: 'Income statement and balance sheet for a given period',
                icon: 'fa-file-invoice',
                categories: ['financial', 'statements']
            },
            {
                id: 'transaction_log',
                name: 'Transaction Log',
                description: 'Detailed log of all financial transactions',
                icon: 'fa-exchange-alt',
                categories: ['transactions', 'audit']
            },
            {
                id: 'audit_trail',
                name: 'Audit Trail',
                description: 'System activity log with user actions',
                icon: 'fa-history',
                categories: ['audit', 'security']
            },
            {
                id: 'custom_report',
                name: 'Custom Report',
                description: 'Create a custom report with selected fields and filters',
                icon: 'fa-sliders-h',
                categories: ['custom']
            }
        ];
    }

    // Get report filters based on report type
    getReportFilters(reportType) {
        const commonFilters = [
            {
                id: 'start_date',
                type: 'date',
                label: 'Start Date',
                required: true
            },
            {
                id: 'end_date',
                type: 'date',
                label: 'End Date',
                required: true
            },
            {
                id: 'format',
                type: 'select',
                label: 'Format',
                required: true,
                options: [
                    { value: 'pdf', label: 'PDF' },
                    { value: 'excel', label: 'Excel' },
                    { value: 'csv', label: 'CSV' }
                ],
                defaultValue: 'pdf'
            }
        ];

        const reportSpecificFilters = {
            loan_portfolio: [
                {
                    id: 'status',
                    type: 'select',
                    label: 'Loan Status',
                    multiple: true,
                    options: [
                        { value: 'disbursed', label: 'Disbursed' },
                        { value: 'closed', label: 'Closed' },
                        { value: 'written_off', label: 'Written Off' },
                        { value: 'rejected', label: 'Rejected' },
                        { value: 'pending', label: 'Pending' }
                    ]
                },
                {
                    id: 'officer_id',
                    type: 'select',
                    label: 'Loan Officer',
                    options: 'users', // This would be populated dynamically
                    multiple: true
                }
            ],
            client_listing: [
                {
                    id: 'status',
                    type: 'select',
                    label: 'Client Status',
                    multiple: true,
                    options: [
                        { value: 'active', label: 'Active' },
                        { value: 'inactive', label: 'Inactive' },
                        { value: 'blacklisted', label: 'Blacklisted' }
                    ]
                },
                {
                    id: 'branch_id',
                    type: 'select',
                    label: 'Branch',
                    options: 'branches', // This would be populated dynamically
                    multiple: true
                }
            ],
            arrears_report: [
                {
                    id: 'days_late_min',
                    type: 'number',
                    label: 'Minimum Days Late',
                    min: 1,
                    defaultValue: 1
                },
                {
                    id: 'days_late_max',
                    type: 'number',
                    label: 'Maximum Days Late',
                    min: 1
                },
                {
                    id: 'include_partial_payments',
                    type: 'checkbox',
                    label: 'Include Loans with Partial Payments',
                    defaultValue: false
                }
            ]
        };

        return [
            ...commonFilters,
            ...(reportSpecificFilters[reportType] || [])
        ];
    }

    // Format report data for display
    formatReportData(reportType, data) {
        const formatters = {
            loan_portfolio: this.formatLoanPortfolioData,
            client_listing: this.formatClientListingData,
            arrears_report: this.formatArrearsReportData,
            default: (data) => data
        };

        const formatter = formatters[reportType] || formatters.default;
        return formatter(data);
    }

    // Format loan portfolio data
    formatLoanPortfolioData(data) {
        if (!data || !Array.isArray(data)) return [];
        
        return data.map(loan => ({
            ...loan,
            formatted_amount: formatCurrency(loan.amount),
            formatted_disbursed_date: formatDate(loan.disbursed_date),
            formatted_maturity_date: formatDate(loan.maturity_date),
            formatted_outstanding: formatCurrency(loan.outstanding_balance),
            status_badge: this.getLoanStatusBadge(loan.status),
            days_in_arrears: loan.days_in_arrears || 0,
            formatted_last_payment: loan.last_payment_date ? 
                `${formatDate(loan.last_payment_date)} (${formatCurrency(loan.last_payment_amount)})` : 'N/A'
        }));
    }

    // Format client listing data
    formatClientListingData(data) {
        if (!data || !Array.isArray(data)) return [];
        
        return data.map(client => ({
            ...client,
            full_name: `${client.first_name || ''} ${client.last_name || ''}`.trim(),
            formatted_join_date: formatDate(client.join_date),
            status_badge: this.getClientStatusBadge(client.status),
            contact_info: [
                client.phone_number,
                client.email
            ].filter(Boolean).join(' | '),
            formatted_last_activity: client.last_activity_date ? 
                formatDate(client.last_activity_date, 'DD/MM/YYYY HH:mm') : 'Never'
        }));
    }

    // Format arrears report data
    formatArrearsReportData(data) {
        if (!data || !Array.isArray(data)) return [];
        
        return data.map(item => ({
            ...item,
            formatted_principal: formatCurrency(item.principal),
            formatted_arrears: formatCurrency(item.arrears_amount),
            days_in_arrears: item.days_in_arrears,
            severity: this.getArrearsSeverity(item.days_in_arrears),
            formatted_last_payment: item.last_payment_date ? 
                `${formatDate(item.last_payment_date)} (${formatCurrency(item.last_payment_amount)})` : 'N/A',
            client_name: `${item.client_first_name || ''} ${item.client_last_name || ''}`.trim()
        }));
    }

    // Helper methods for formatting
    getLoanStatusBadge(status) {
        const statusMap = {
            'disbursed': { class: 'primary', label: 'Disbursed' },
            'closed': { class: 'success', label: 'Closed' },
            'written_off': { class: 'dark', label: 'Written Off' },
            'rejected': { class: 'danger', label: 'Rejected' },
            'pending': { class: 'warning', label: 'Pending' }
        };

        const statusInfo = statusMap[status] || { class: 'secondary', label: status };
        return `<span class="badge bg-${statusInfo.class}">${statusInfo.label}</span>`;
    }

    getClientStatusBadge(status) {
        const statusMap = {
            'active': { class: 'success', label: 'Active' },
            'inactive': { class: 'secondary', label: 'Inactive' },
            'blacklisted': { class: 'danger', label: 'Blacklisted' },
            'pending': { class: 'warning', label: 'Pending' }
        };

        const statusInfo = statusMap[status] || { class: 'secondary', label: status };
        return `<span class="badge bg-${statusInfo.class}">${statusInfo.label}</span>`;
    }

    getArrearsSeverity(days) {
        if (days >= 90) return 'high';
        if (days >= 30) return 'medium';
        if (days > 0) return 'low';
        return 'none';
    }

    // Export report data to different formats
    exportReport(data, format, fileName = 'report') {
        switch (format.toLowerCase()) {
            case 'pdf':
                this.exportToPdf(data, fileName);
                break;
            case 'excel':
                this.exportToExcel(data, fileName);
                break;
            case 'csv':
                this.exportToCsv(data, fileName);
                break;
            default:
                console.error('Unsupported export format:', format);
        }
    }

    // Export to PDF (placeholder - would use a PDF library in a real implementation)
    exportToPdf(data, fileName) {
        console.log('Exporting to PDF:', data);
        // In a real app, this would use a library like jsPDF or html2pdf
        alert(`Exporting ${data.length} records to PDF (${fileName}.pdf)`);
    }

    // Export to Excel (placeholder - would use a library like xlsx)
    exportToExcel(data, fileName) {
        console.log('Exporting to Excel:', data);
        // In a real app, this would use a library like xlsx
        alert(`Exporting ${data.length} records to Excel (${fileName}.xlsx)`);
    }

    // Export to CSV
    exportToCsv(data, fileName) {
        if (!data || !data.length) {
            console.error('No data to export');
            return;
        }

        // Get headers from the first object
        const headers = Object.keys(data[0]);
        
        // Create CSV content
        let csvContent = headers.join(',') + '\n';
        
        // Add rows
        data.forEach(row => {
            const values = headers.map(header => {
                // Escape quotes and wrap in quotes
                const value = row[header] !== undefined ? row[header] : '';
                return `"${String(value).replace(/"/g, '""')}"`;
            });
            csvContent += values.join(',') + '\n';
        });

        // Create download link
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const url = URL.createObjectURL(blob);
        const link = document.createElement('a');
        
        link.setAttribute('href', url);
        link.setAttribute('download', `${fileName}.csv`);
        link.style.visibility = 'hidden';
        
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
    }
}

// Create a singleton instance
const reportService = new ReportService();

export default reportService;
