<?php
require_once 'BaseController.php';

class ClientController extends BaseController {
    private $table_name = 'clients';
    
    public function __construct() {
        parent::__construct();
    }
    
    public function getAll() {
        $query = "SELECT * FROM " . $this->table_name . " ORDER BY created_at DESC";
        $stmt = $this->db->prepare($query);
        $stmt->execute();
        
        $clients = [];
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $clients[] = $this->formatClientData($row);
        }
        
        $this->sendResponse(200, $clients);
    }
    
    public function get($id) {
        $query = "SELECT * FROM " . $this->table_name . " WHERE id = :id LIMIT 1";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();
        
        if ($stmt->rowCount() > 0) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            $this->sendResponse(200, $this->formatClientData($row));
        } else {
            $this->sendResponse(404, ['message' => 'Client not found']);
        }
    }
    
    public function create($data) {
        // Validate input
        $required_fields = ['first_name', 'last_name', 'email', 'phone'];
        $missing_fields = [];
        
        foreach ($required_fields as $field) {
            if (empty($data[$field])) {
                $missing_fields[] = $field;
            }
        }
        
        if (!empty($missing_fields)) {
            $this->sendResponse(400, [
                'status' => 'error',
                'message' => 'Missing required fields',
                'missing_fields' => $missing_fields
            ]);
            return;
        }
        
        // Check if email already exists
        $query = "SELECT id FROM " . $this->table_name . " WHERE email = :email LIMIT 1";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':email', $data['email']);
        $stmt->execute();
        
        if ($stmt->rowCount() > 0) {
            $this->sendResponse(400, [
                'status' => 'error',
                'message' => 'Email already exists'
            ]);
            return;
        }
        
        // Generate client ID
        $client_id = 'CLI' . strtoupper(uniqid());
        
        // Insert new client
        $query = "INSERT INTO " . $this->table_name . " 
                 (client_id, first_name, last_name, email, phone, address, date_of_birth, id_number, status, created_by)
                 VALUES 
                 (:client_id, :first_name, :last_name, :email, :phone, :address, :date_of_birth, :id_number, 'active', :created_by)";
        
        $stmt = $this->db->prepare($query);
        
        // Bind parameters
        $stmt->bindParam(':client_id', $client_id);
        $stmt->bindParam(':first_name', $data['first_name']);
        $stmt->bindParam(':last_name', $data['last_name']);
        $stmt->bindParam(':email', $data['email']);
        $stmt->bindParam(':phone', $data['phone']);
        $stmt->bindParam(':address', $data['address'] ?? null);
        $stmt->bindParam(':date_of_birth', $data['date_of_birth'] ?? null);
        $stmt->bindParam(':id_number', $data['id_number'] ?? null);
        $stmt->bindParam(':created_by', $this->user_data['id']);
        
        if ($stmt->execute()) {
            $client_id = $this->db->lastInsertId();
            $this->logActivity('Created client ' . $data['first_name'] . ' ' . $data['last_name']);
            
            // Get the newly created client
            $query = "SELECT * FROM " . $this->table_name . " WHERE id = :id";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':id', $client_id);
            $stmt->execute();
            
            $client = $stmt->fetch(PDO::FETCH_ASSOC);
            
            $this->sendResponse(201, [
                'status' => 'success',
                'message' => 'Client created successfully',
                'data' => $this->formatClientData($client)
            ]);
        } else {
            $this->sendResponse(500, [
                'status' => 'error',
                'message' => 'Failed to create client'
            ]);
        }
    }
    
    public function update($id, $data) {
        // Check if client exists
        $query = "SELECT * FROM " . $this->table_name . " WHERE id = :id LIMIT 1";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();
        
        if ($stmt->rowCount() === 0) {
            $this->sendResponse(404, [
                'status' => 'error',
                'message' => 'Client not found'
            ]);
            return;
        }
        
        $client = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // Build update query
        $updates = [];
        $params = [':id' => $id];
        
        $updatable_fields = ['first_name', 'last_name', 'email', 'phone', 'address', 'date_of_birth', 'id_number', 'status'];
        
        foreach ($updatable_fields as $field) {
            if (isset($data[$field])) {
                $updates[] = "$field = :$field";
                $params[":$field"] = $data[$field];
            }
        }
        
        if (empty($updates)) {
            $this->sendResponse(400, [
                'status' => 'error',
                'message' => 'No valid fields to update'
            ]);
            return;
        }
        
        // Check if email is being updated and already exists
        if (isset($data['email']) && $data['email'] !== $client['email']) {
            $query = "SELECT id FROM " . $this->table_name . " WHERE email = :email AND id != :id LIMIT 1";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':email', $data['email']);
            $stmt->bindParam(':id', $id);
            $stmt->execute();
            
            if ($stmt->rowCount() > 0) {
                $this->sendResponse(400, [
                    'status' => 'error',
                    'message' => 'Email already exists'
                ]);
                return;
            }
        }
        
        // Update client
        $query = "UPDATE " . $this->table_name . " SET " . implode(', ', $updates) . " WHERE id = :id";
        $stmt = $this->db->prepare($query);
        
        if ($stmt->execute($params)) {
            $this->logActivity('Updated client ' . ($data['first_name'] ?? $client['first_name']) . ' ' . ($data['last_name'] ?? $client['last_name']));
            
            // Get the updated client
            $query = "SELECT * FROM " . $this->table_name . " WHERE id = :id";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':id', $id);
            $stmt->execute();
            
            $updated_client = $stmt->fetch(PDO::FETCH_ASSOC);
            
            $this->sendResponse(200, [
                'status' => 'success',
                'message' => 'Client updated successfully',
                'data' => $this->formatClientData($updated_client)
            ]);
        } else {
            $this->sendResponse(500, [
                'status' => 'error',
                'message' => 'Failed to update client'
            ]);
        }
    }
    
    public function delete($id) {
        // Check if client exists
        $query = "SELECT * FROM " . $this->table_name . " WHERE id = :id LIMIT 1";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();
        
        if ($stmt->rowCount() === 0) {
            $this->sendResponse(404, [
                'status' => 'error',
                'message' => 'Client not found'
            ]);
            return;
        }
        
        $client = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // Check if client has any active loans
        $query = "SELECT id FROM loans WHERE client_id = :client_id AND status IN ('pending', 'approved', 'disbursed') LIMIT 1";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':client_id', $id);
        $stmt->execute();
        
        if ($stmt->rowCount() > 0) {
            $this->sendResponse(400, [
                'status' => 'error',
                'message' => 'Cannot delete client with active loans'
            ]);
            return;
        }
        
        // Delete client (in a real application, you might want to soft delete)
        $query = "DELETE FROM " . $this->table_name . " WHERE id = :id";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':id', $id);
        
        if ($stmt->execute()) {
            $this->logActivity('Deleted client ' . $client['first_name'] . ' ' . $client['last_name']);
            
            $this->sendResponse(200, [
                'status' => 'success',
                'message' => 'Client deleted successfully'
            ]);
        } else {
            $this->sendResponse(500, [
                'status' => 'error',
                'message' => 'Failed to delete client'
            ]);
        }
    }
    
    private function formatClientData($client) {
        return [
            'id' => (int)$client['id'],
            'client_id' => $client['client_id'],
            'first_name' => $client['first_name'],
            'last_name' => $client['last_name'],
            'full_name' => $client['first_name'] . ' ' . $client['last_name'],
            'email' => $client['email'],
            'phone' => $client['phone'],
            'address' => $client['address'],
            'date_of_birth' => $client['date_of_birth'],
            'id_number' => $client['id_number'],
            'status' => $client['status'],
            'created_at' => $client['created_at'],
            'updated_at' => $client['updated_at']
        ];
    }
}
