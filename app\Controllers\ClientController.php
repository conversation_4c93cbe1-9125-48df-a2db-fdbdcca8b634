<?php

class ClientController extends BaseController {
    public function index() {
        try {
            // Get query parameters
            $params = $this->request->getQueryParams();
            $page = max(1, (int) ($params['page'] ?? 1));
            $perPage = min(50, max(1, (int) ($params['per_page'] ?? 10)));
            $offset = ($page - 1) * $perPage;
            $search = $params['search'] ?? '';
            $status = $params['status'] ?? '';
            
            // Build base query
            $query = "SELECT c.*, 
                     (SELECT COUNT(*) FROM loans WHERE client_id = c.id AND status = 'active') as active_loans,
                     (SELECT COALESCE(SUM(amount), 0) FROM loans WHERE client_id = c.id AND status = 'disbursed') as total_borrowed,
                     (SELECT COALESCE(SUM(p.amount), 0) FROM payments p 
                      JOIN loans l ON p.loan_id = l.id 
                      WHERE l.client_id = c.id) as total_repaid
                     FROM clients c";
            
            $countQuery = "SELECT COUNT(*) as total FROM clients c";
            $where = [];
            $queryParams = [];
            
            // Apply filters
            if (!empty($search)) {
                $where[] = "(c.first_name LIKE ? OR c.last_name LIKE ? OR c.phone LIKE ? OR c.email LIKE ? OR c.id_number LIKE ?)";
                $searchTerm = "%{$search}%";
                $queryParams = array_merge($queryParams, array_fill(0, 5, $searchTerm));
            }
            
            if (!empty($status)) {
                $where[] = "c.status = ?";
                $queryParams[] = $status;
            }
            
            // Add WHERE clause if filters exist
            if (!empty($where)) {
                $whereClause = " WHERE " . implode(" AND ", $where);
                $query .= $whereClause;
                $countQuery .= $whereClause;
            }
            
            // Add ordering and pagination
            $query .= " ORDER BY c.created_at DESC LIMIT ? OFFSET ?";
            $countParams = $queryParams;
            $queryParams[] = $perPage;
            $queryParams[] = $offset;
            
            // Get total count
            $stmt = $this->db->prepare($countQuery);
            $stmt->execute($countParams);
            $total = $stmt->fetch()['total'];
            
            // Get paginated results
            $stmt = $this->db->prepare($query);
            $stmt->execute($queryParams);
            $clients = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Calculate pagination metadata
            $lastPage = ceil($total / $perPage);
            
            return $this->success([
                'data' => $clients,
                'meta' => [
                    'current_page' => $page,
                    'per_page' => $perPage,
                    'total' => (int) $total,
                    'last_page' => $lastPage,
                    'from' => $offset + 1,
                    'to' => min($offset + $perPage, $total)
                ]
            ]);
            
        } catch (Exception $e) {
            return $this->error('Failed to fetch clients: ' . $e->getMessage());
        }
    }
    
    public function show($id) {
        try {
            // Get client details
            $stmt = $this->db->prepare("SELECT * FROM clients WHERE id = ?");
            $stmt->execute([$id]);
            $client = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$client) {
                return $this->error('Client not found', 404);
            }
            
            // Get client's loans summary
            $stmt = $this->db->prepare("
                SELECT 
                    COUNT(*) as total_loans,
                    SUM(CASE WHEN status = 'disbursed' THEN 1 ELSE 0 END) as active_loans,
                    SUM(CASE WHEN status = 'closed' THEN 1 ELSE 0 END) as closed_loans,
                    SUM(CASE WHEN status = 'written_off' THEN 1 ELSE 0 END) as written_off_loans,
                    COALESCE(SUM(amount), 0) as total_borrowed,
                    COALESCE(SUM(total_payable - amount), 0) as total_interest
                FROM loans 
                WHERE client_id = ?
            ");
            $stmt->execute([$id]);
            $loansSummary = $stmt->fetch(PDO::FETCH_ASSOC);
            
            // Get client's recent loans
            $stmt = $this->db->prepare("
                SELECT l.*, lp.name as product_name 
                FROM loans l
                JOIN loan_products lp ON l.product_id = lp.id
                WHERE l.client_id = ?
                ORDER BY l.created_at DESC
                LIMIT 5
            ");
            $stmt->execute([$id]);
            $recentLoans = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Get client's recent payments
            $stmt = $this->db->prepare("
                SELECT p.*, l.loan_number
                FROM payments p
                JOIN loans l ON p.loan_id = l.id
                WHERE l.client_id = ?
                ORDER BY p.payment_date DESC, p.created_at DESC
                LIMIT 5
            ");
            $stmt->execute([$id]);
            $recentPayments = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Add additional data to client
            $client['loans_summary'] = $loansSummary;
            $client['recent_loans'] = $recentLoans;
            $client['recent_payments'] = $recentPayments;
            
            return $this->success($client);
            
        } catch (Exception $e) {
            return $this->error('Failed to fetch client details: ' . $e->getMessage());
        }
    }
    
    public function store() {
        $data = $this->request->getParsedBody();
        
        // Validate input
        $validation = $this->validate([
            'first_name' => 'required|min:2|max:50',
            'last_name' => 'required|min:2|max:50',
            'email' => 'email|unique:clients,email',
            'phone' => 'required|min:10|max:20',
            'date_of_birth' => 'date',
            'gender' => 'in:male,female,other',
            'marital_status' => 'in:single,married,divorced,widowed',
            'id_type' => 'in:national_id,passport,driving_license,other',
            'id_number' => 'string|max:50|unique:clients,id_number',
            'occupation' => 'string|max:100',
            'monthly_income' => 'numeric|min:0',
            'address' => 'string|max:255',
            'city' => 'string|max:100',
            'state' => 'string|max:100',
            'postal_code' => 'string|max:20',
            'country' => 'string|max:100',
            'status' => 'in:active,inactive,blacklisted',
            'photo' => 'string|max:255'
        ]);
        
        if ($validation !== true) {
            return $validation;
        }
        
        try {
            // Start transaction
            $this->db->beginTransaction();
            
            // Generate client ID
            $clientId = 'CL-' . strtoupper(uniqid());
            
            // Prepare data for insertion
            $clientData = [
                'client_id' => $clientId,
                'first_name' => $data['first_name'],
                'last_name' => $data['last_name'],
                'email' => $data['email'] ?? null,
                'phone' => $data['phone'],
                'date_of_birth' => !empty($data['date_of_birth']) ? $data['date_of_birth'] : null,
                'gender' => $data['gender'] ?? null,
                'marital_status' => $data['marital_status'] ?? null,
                'id_type' => $data['id_type'] ?? null,
                'id_number' => $data['id_number'] ?? null,
                'occupation' => $data['occupation'] ?? null,
                'monthly_income' => $data['monthly_income'] ?? 0,
                'address' => $data['address'] ?? null,
                'city' => $data['city'] ?? null,
                'state' => $data['state'] ?? null,
                'postal_code' => $data['postal_code'] ?? null,
                'country' => $data['country'] ?? null,
                'photo_url' => $data['photo'] ?? null,
                'status' => $data['status'] ?? 'active',
                'created_by' => $this->getCurrentUserId()
            ];
            
            // Build SQL query
            $columns = implode(', ', array_keys($clientData));
            $placeholders = implode(', ', array_fill(0, count($clientData), '?'));
            
            $stmt = $this->db->prepare("INSERT INTO clients ($columns) VALUES ($placeholders)");
            $stmt->execute(array_values($clientData));
            
            $clientId = $this->db->lastInsertId();
            
            // Commit transaction
            $this->db->commit();
            
            // Return the created client
            return $this->show($clientId);
            
        } catch (Exception $e) {
            // Rollback transaction on error
            if ($this->db->inTransaction()) {
                $this->db->rollBack();
            }
            
            return $this->error('Failed to create client: ' . $e->getMessage());
        }
    }
    
    public function update($id) {
        $data = $this->request->getParsedBody();
        
        // Validate input
        $validation = $this->validate([
            'first_name' => 'min:2|max:50',
            'last_name' => 'min:2|max:50',
            'email' => 'email|unique:clients,email,' . $id,
            'phone' => 'min:10|max:20',
            'date_of_birth' => 'date',
            'gender' => 'in:male,female,other',
            'marital_status' => 'in:single,married,divorced,widowed',
            'id_type' => 'in:national_id,passport,driving_license,other',
            'id_number' => 'string|max:50|unique:clients,id_number,' . $id,
            'occupation' => 'string|max:100',
            'monthly_income' => 'numeric|min:0',
            'address' => 'string|max:255',
            'city' => 'string|max:100',
            'state' => 'string|max:100',
            'postal_code' => 'string|max:20',
            'country' => 'string|max:100',
            'status' => 'in:active,inactive,blacklisted',
            'photo' => 'string|max:255'
        ]);
        
        if ($validation !== true) {
            return $validation;
        }
        
        try {
            // Start transaction
            $this->db->beginTransaction();
            
            // Check if client exists
            $stmt = $this->db->prepare("SELECT id FROM clients WHERE id = ?");
            $stmt->execute([$id]);
            
            if (!$stmt->fetch()) {
                return $this->error('Client not found', 404);
            }
            
            // Prepare data for update
            $updateFields = [];
            $updateParams = [];
            
            $allowedFields = [
                'first_name', 'last_name', 'email', 'phone', 'date_of_birth',
                'gender', 'marital_status', 'id_type', 'id_number', 'occupation',
                'monthly_income', 'address', 'city', 'state', 'postal_code',
                'country', 'photo_url', 'status'
            ];
            
            foreach ($allowedFields as $field) {
                if (array_key_exists($field, $data)) {
                    $updateFields[] = "$field = ?";
                    $updateParams[] = $data[$field];
                }
            }
            
            if (empty($updateFields)) {
                return $this->error('No valid fields to update', 400);
            }
            
            // Add updated_at
            $updateFields[] = 'updated_at = NOW()';
            
            // Build and execute update query
            $updateQuery = "UPDATE clients SET " . implode(', ', $updateFields) . " WHERE id = ?";
            $updateParams[] = $id;
            
            $stmt = $this->db->prepare($updateQuery);
            $stmt->execute($updateParams);
            
            // Commit transaction
            $this->db->commit();
            
            // Return the updated client
            return $this->show($id);
            
        } catch (Exception $e) {
            // Rollback transaction on error
            if ($this->db->inTransaction()) {
                $this->db->rollBack();
            }
            
            return $this->error('Failed to update client: ' . $e->getMessage());
        }
    }
    
    public function uploadPhoto() {
        try {
            // Check if file was uploaded
            if (!isset($_FILES['photo']) || $_FILES['photo']['error'] !== UPLOAD_ERR_OK) {
                return $this->error('No file uploaded or upload error', 400);
            }
            
            $file = $_FILES['photo'];
            
            // Validate file type
            $allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
            $fileType = mime_content_type($file['tmp_name']);
            
            if (!in_array($fileType, $allowedTypes)) {
                return $this->error('Invalid file type. Only JPG, PNG, and GIF are allowed.', 400);
            }
            
            // Validate file size (max 2MB)
            $maxSize = 2 * 1024 * 1024; // 2MB
            if ($file['size'] > $maxSize) {
                return $this->error('File is too large. Maximum size is 2MB.', 400);
            }
            
            // Create uploads directory if it doesn't exist
            $uploadDir = __DIR__ . '/../../public/uploads/clients/';
            if (!is_dir($uploadDir)) {
                mkdir($uploadDir, 0755, true);
            }
            
            // Generate unique filename
            $extension = pathinfo($file['name'], PATHINFO_EXTENSION);
            $filename = 'client_' . uniqid() . '.' . $extension;
            $destination = $uploadDir . $filename;
            
            // Move uploaded file
            if (!move_uploaded_file($file['tmp_name'], $destination)) {
                throw new Exception('Failed to move uploaded file');
            }
            
            // Return the relative path
            $relativePath = '/uploads/clients/' . $filename;
            
            return $this->success([
                'path' => $relativePath,
                'url' => $this->getBaseUrl() . $relativePath
            ]);
            
        } catch (Exception $e) {
            return $this->error('Failed to upload photo: ' . $e->getMessage());
        }
    }
    
    private function getCurrentUserId() {
        // This would typically come from the JWT token in the request
        $token = JWT::getTokenFromHeader();
        if ($token) {
            $decoded = JWT::decode($token);
            return $decoded['user_id'] ?? null;
        }
        return null;
    }
    
    private function getBaseUrl() {
        $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https://' : 'http://';
        $host = $_SERVER['HTTP_HOST'];
        $scriptName = dirname($_SERVER['SCRIPT_NAME']);
        
        return rtrim($protocol . $host . $scriptName, '/');
    }
}
