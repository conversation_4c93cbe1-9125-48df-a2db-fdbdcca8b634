class ClientService {
    constructor() {
        this.apiService = apiService;
    }

    // Get all clients with optional filters
    async getClients(filters = {}) {
        try {
            const data = await this.apiService.getClients(filters);
            return { success: true, data };
        } catch (error) {
            console.error('Failed to fetch clients:', error);
            return { 
                success: false, 
                error: error.message || 'Failed to fetch clients. Please try again.'
            };
        }
    }

    // Get client by ID
    async getClient(clientId) {
        try {
            const data = await this.apiService.getClient(clientId);
            return { success: true, data };
        } catch (error) {
            console.error(`Failed to fetch client ${clientId}:`, error);
            return { 
                success: false, 
                error: error.message || 'Failed to fetch client details. Please try again.'
            };
        }
    }

    // Create a new client
    async createClient(clientData) {
        try {
            const data = await this.apiService.createClient(clientData);
            return { success: true, data };
        } catch (error) {
            console.error('Failed to create client:', error);
            return { 
                success: false, 
                error: error.message || 'Failed to create client. Please check your input and try again.'
            };
        }
    }

    // Update an existing client
    async updateClient(clientId, clientData) {
        try {
            const data = await this.apiService.updateClient(clientId, clientData);
            return { success: true, data };
        } catch (error) {
            console.error(`Failed to update client ${clientId}:`, error);
            return { 
                success: false, 
                error: error.message || 'Failed to update client. Please try again.'
            };
        }
    }

    // Search clients by name, ID, or contact info
    async searchClients(query, limit = 10) {
        try {
            const data = await this.apiService.getClients({
                search: query,
                limit: limit
            });
            return { success: true, data };
        } catch (error) {
            console.error('Client search failed:', error);
            return { success: false, error: 'Search failed. Please try again.' };
        }
    }

    // Get client summary for dashboard
    async getClientSummary() {
        try {
            const [allClients, activeClients, inactiveClients] = await Promise.all([
                this.apiService.getClients({ limit: 1 }),
                this.apiService.getClients({ status: 'active', limit: 1 }),
                this.apiService.getClients({ status: 'inactive', limit: 1 })
            ]);

            return {
                success: true,
                data: {
                    totalClients: allClients.total || 0,
                    activeClients: activeClients.total || 0,
                    inactiveClients: inactiveClients.total || 0
                }
            };
        } catch (error) {
            console.error('Failed to fetch client summary:', error);
            return { success: false, error: 'Failed to load client summary' };
        }
    }

    // Format client data for display
    formatClient(client) {
        if (!client) return null;
        
        return {
            ...client,
            fullName: `${client.firstName || ''} ${client.lastName || ''}`.trim(),
            formattedJoinDate: formatDate(client.joinDate),
            statusBadge: this.getStatusBadge(client.status),
            formattedLastActive: client.lastActive ? formatDate(client.lastActive, 'DD/MM/YYYY HH:mm') : 'Never'
        };
    }

    // Get status badge HTML
    getStatusBadge(status) {
        const statusMap = {
            'active': { class: 'success', label: 'Active' },
            'inactive': { class: 'secondary', label: 'Inactive' },
            'blacklisted': { class: 'danger', label: 'Blacklisted' },
            'pending': { class: 'warning', label: 'Pending' }
        };

        const statusInfo = statusMap[status] || { class: 'secondary', label: status };
        return `<span class="badge bg-${statusInfo.class}">${statusInfo.label}</span>`;
    }

    // Validate client data before submission
    validateClientData(clientData, isNew = true) {
        const errors = [];
        
        if (isNew) {
            if (!clientData.firstName?.trim()) {
                errors.push('First name is required');
            }
            if (!clientData.lastName?.trim()) {
                errors.push('Last name is required');
            }
        }

        if (clientData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(clientData.email)) {
            errors.push('Please enter a valid email address');
        }

        if (clientData.phone && !/^[0-9+\-\s()]+$/.test(clientData.phone)) {
            errors.push('Please enter a valid phone number');
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }

    // Get client's loan summary
    async getClientLoanSummary(clientId) {
        try {
            const [activeLoans, completedLoans, pendingLoans] = await Promise.all([
                this.apiService.getLoans({ clientId, status: 'disbursed' }),
                this.apiService.getLoans({ clientId, status: 'closed' }),
                this.apiService.getLoans({ clientId, status: 'pending' })
            ]);

            const totalBorrowed = completedLoans.data.reduce((sum, loan) => sum + loan.amount, 0);
            const totalOutstanding = activeLoans.data.reduce((sum, loan) => {
                return sum + (loan.amount - (loan.amountPaid || 0));
            }, 0);

            return {
                success: true,
                data: {
                    activeLoans: activeLoans.total || 0,
                    completedLoans: completedLoans.total || 0,
                    pendingLoans: pendingLoans.total || 0,
                    totalBorrowed,
                    totalOutstanding,
                    formattedTotalBorrowed: formatCurrency(totalBorrowed),
                    formattedTotalOutstanding: formatCurrency(totalOutstanding)
                }
            };
        } catch (error) {
            console.error(`Failed to fetch loan summary for client ${clientId}:`, error);
            return { success: false, error: 'Failed to load loan summary' };
        }
    }
}

// Create a singleton instance
const clientService = new ClientService();

export default clientService;
