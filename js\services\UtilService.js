class UtilService {
    constructor() {
        // Initialize any required properties
    }

    // Format currency with custom symbol and decimals
    formatCurrency(amount, currency = APP_CONFIG.CURRENCY, decimals = 2) {
        if (amount === null || amount === undefined) return `${currency}0.00`;
        
        const numericAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
        
        if (isNaN(numericAmount)) return `${currency}0.00`;
        
        return `${currency}${numericAmount.toLocaleString(undefined, {
            minimumFractionDigits: decimals,
            maximumFractionDigits: decimals
        })}`;
    }

    // Format date with custom format
    formatDate(dateString, format = APP_CONFIG.DATE_FORMAT) {
        if (!dateString) return '';
        
        const date = new Date(dateString);
        if (isNaN(date.getTime())) return '';
        
        const day = String(date.getDate()).padStart(2, '0');
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const year = date.getFullYear();
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        const seconds = String(date.getSeconds()).padStart(2, '0');
        
        return format
            .replace('DD', day)
            .replace('MM', month)
            .replace('YYYY', year)
            .replace('HH', hours)
            .replace('mm', minutes)
            .replace('ss', seconds);
    }

    // Calculate age from date of birth
    calculateAge(dateOfBirth) {
        if (!dateOfBirth) return null;
        
        const birthDate = new Date(dateOfBirth);
        if (isNaN(birthDate.getTime())) return null;
        
        const today = new Date();
        let age = today.getFullYear() - birthDate.getFullYear();
        const monthDiff = today.getMonth() - birthDate.getMonth();
        
        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
            age--;
        }
        
        return age;
    }

    // Generate a unique ID
    generateId(prefix = '') {
        return `${prefix}${Date.now().toString(36)}${Math.random().toString(36).substr(2, 5)}`;
    }

    // Debounce function to limit how often a function can be called
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Throttle function to limit the rate at which a function can be called
    throttle(func, limit) {
        let inThrottle;
        return function executedFunction(...args) {
            if (!inThrottle) {
                func(...args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    // Validate email address
    validateEmail(email) {
        const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return re.test(String(email).toLowerCase());
    }

    // Validate phone number (basic validation)
    validatePhone(phone) {
        const re = /^[0-9+\-\s()]+$/;
        return re.test(phone);
    }

    // Format phone number (basic formatting)
    formatPhoneNumber(phone) {
        if (!phone) return '';
        // Remove all non-digit characters
        const cleaned = ('' + phone).replace(/\D/g, '');
        
        // Check if the number starts with a country code
        if (cleaned.length > 10) {
            // Format as international number
            return `+${cleaned}`;
        } else {
            // Format as local number (assuming US/Canada format)
            const match = cleaned.match(/^(\d{3})(\d{3})(\d{4})$/);
            if (match) {
                return `(${match[1]}) ${match[2]}-${match[3]}`;
            }
        }
        
        // Return original if formatting fails
        return phone;
    }

    // Truncate text with ellipsis
    truncateText(text, maxLength = 100, ellipsis = '...') {
        if (!text) return '';
        if (text.length <= maxLength) return text;
        return text.substring(0, maxLength) + ellipsis;
    }

    // Convert object to query string
    objectToQueryString(obj) {
        if (!obj) return '';
        return Object.entries(obj)
            .filter(([_, value]) => value !== null && value !== undefined)
            .map(([key, value]) => {
                if (Array.isArray(value)) {
                    return value.map(v => `${encodeURIComponent(key)}[]=${encodeURIComponent(v)}`).join('&');
                }
                return `${encodeURIComponent(key)}=${encodeURIComponent(value)}`;
            })
            .join('&');
    }

    // Parse query string to object
    parseQueryString(queryString) {
        const params = {};
        new URLSearchParams(queryString).forEach((value, key) => {
            if (key.endsWith('[]')) {
                const cleanKey = key.replace(/\[\]$/, '');
                if (!params[cleanKey]) {
                    params[cleanKey] = [];
                }
                params[cleanKey].push(value);
            } else {
                params[key] = value;
            }
        });
        return params;
    }

    // Deep clone an object
    deepClone(obj) {
        if (obj === null || typeof obj !== 'object') {
            return obj;
        }
        
        if (obj instanceof Date) {
            return new Date(obj);
        }
        
        if (Array.isArray(obj)) {
            const arrCopy = [];
            for (let i = 0; i < obj.length; i++) {
                arrCopy[i] = this.deepClone(obj[i]);
            }
            return arrCopy;
        }
        
        const objCopy = {};
        for (const key in obj) {
            if (obj.hasOwnProperty(key)) {
                objCopy[key] = this.deepClone(obj[key]);
            }
        }
        
        return objCopy;
    }

    // Merge objects deeply
    deepMerge(target, ...sources) {
        if (!sources.length) return target;
        const source = sources.shift();

        if (this.isObject(target) && this.isObject(source)) {
            for (const key in source) {
                if (this.isObject(source[key])) {
                    if (!target[key]) Object.assign(target, { [key]: {} });
                    this.deepMerge(target[key], source[key]);
                } else if (Array.isArray(source[key]) && Array.isArray(target[key])) {
                    target[key] = [...target[key], ...source[key]];
                } else {
                    Object.assign(target, { [key]: source[key] });
                }
            }
        }

        return this.deepMerge(target, ...sources);
    }

    // Check if value is an object
    isObject(item) {
        return (item && typeof item === 'object' && !Array.isArray(item));
    }

    // Generate a random string of specified length
    randomString(length = 10) {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let result = '';
        for (let i = 0; i < length; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }

    // Generate a random number within a range
    randomNumber(min, max) {
        return Math.floor(Math.random() * (max - min + 1)) + min;
    }

    // Format bytes to human-readable format
    formatBytes(bytes, decimals = 2) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const dm = decimals < 0 ? 0 : decimals;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
        
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
    }

    // Convert a file to base64
    fileToBase64(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.readAsDataURL(file);
            reader.onload = () => resolve(reader.result);
            reader.onerror = error => reject(error);
        });
    }

    // Download content as a file
    downloadFile(content, fileName, contentType = 'text/plain') {
        const a = document.createElement('a');
        const file = new Blob([content], { type: contentType });
        a.href = URL.createObjectURL(file);
        a.download = fileName;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(a.href);
    }

    // Copy text to clipboard
    copyToClipboard(text) {
        return new Promise((resolve, reject) => {
            if (navigator.clipboard) {
                navigator.clipboard.writeText(text).then(resolve).catch(reject);
            } else {
                const textarea = document.createElement('textarea');
                textarea.value = text;
                textarea.style.position = 'fixed';
                document.body.appendChild(textarea);
                textarea.select();
                
                try {
                    const successful = document.execCommand('copy');
                    document.body.removeChild(textarea);
                    successful ? resolve() : reject(new Error('Copy failed'));
                } catch (err) {
                    document.body.removeChild(textarea);
                    reject(err);
                }
            }
        });
    }

    // Scroll to element with smooth animation
    scrollToElement(selector, offset = 0) {
        const element = document.querySelector(selector);
        if (element) {
            const elementPosition = element.getBoundingClientRect().top;
            const offsetPosition = elementPosition + window.pageYOffset - offset;
            
            window.scrollTo({
                top: offsetPosition,
                behavior: 'smooth'
            });
        }
    }

    // Check if element is in viewport
    isInViewport(element) {
        if (!element) return false;
        
        const rect = element.getBoundingClientRect();
        return (
            rect.top >= 0 &&
            rect.left >= 0 &&
            rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
            rect.right <= (window.innerWidth || document.documentElement.clientWidth)
        );
    }

    // Add class to element if not already present
    addClass(element, className) {
        if (element && !element.classList.contains(className)) {
            element.classList.add(className);
        }
    }

    // Remove class from element if present
    removeClass(element, className) {
        if (element && element.classList.contains(className)) {
            element.classList.remove(className);
        }
    }

    // Toggle class on element
    toggleClass(element, className) {
        if (element) {
            element.classList.toggle(className);
        }
    }

    // Show notification (using SweetAlert2 if available, otherwise alert)
    showNotification(title, message, type = 'info') {
        if (typeof Swal !== 'undefined') {
            const Toast = Swal.mixin({
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
                didOpen: (toast) => {
                    toast.addEventListener('mouseenter', Swal.stopTimer);
                    toast.addEventListener('mouseleave', Swal.resumeTimer);
                }
            });

            Toast.fire({
                icon: type,
                title: title,
                text: message
            });
        } else {
            alert(`${title}: ${message}`);
        }
    }

    // Confirm dialog (using SweetAlert2 if available, otherwise confirm)
    async confirm(title, text, confirmButtonText = 'Yes', cancelButtonText = 'Cancel') {
        if (typeof Swal !== 'undefined') {
            const result = await Swal.fire({
                title: title,
                text: text,
                icon: 'question',
                showCancelButton: true,
                confirmButtonText: confirmButtonText,
                cancelButtonText: cancelButtonText,
                reverseButtons: true
            });
            return result.isConfirmed;
        } else {
            return window.confirm(`${title}\n\n${text}`);
        }
    }
}

// Create a singleton instance
const utilService = new UtilService();

export default utilService;
