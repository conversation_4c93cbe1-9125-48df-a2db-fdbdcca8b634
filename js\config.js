const API_CONFIG = {
    BASE_URL: 'http://localhost/sub%20system/api',
    ENDPOINTS: {
        AUTH: {
            LOGIN: '/auth/login',
            REGISTER: '/auth/register',
            ME: '/auth/me'
        },
        CLIENTS: {
            BASE: '/clients',
            BY_ID: (id) => `/clients/${id}`,
            SEARCH: '/clients/search'
        },
        LOANS: {
            BASE: '/loans',
            BY_ID: (id) => `/loans/${id}`,
            PAYMENTS: (loanId) => `/loans/${loanId}/payments`,
            CALCULATE: '/loans/calculate'
        },
        DASHBOARD: {
            STATS: '/dashboard/stats'
        },
        REPORTS: {
            LOANS: '/reports/loans',
            PAYMENTS: '/reports/payments',
            CLIENTS: '/reports/clients'
        }
    },
    DEFAULT_HEADERS: {
        'Content-Type': 'application/json',
        'Accept': 'application/json'
    },
    getAuthHeader: (token) => ({
        'Authorization': `Bearer ${token}`,
        ...this.DEFAULT_HEADERS
    })
};

const APP_CONFIG = {
    APP_NAME: 'Microfinance Management System',
    CURRENCY: '₦',
    DATE_FORMAT: 'DD/MM/YYYY',
    DATE_TIME_FORMAT: 'DD/MM/YYYY HH:mm',
    ITEMS_PER_PAGE: 10,
    TOKEN_KEY: 'mfs_auth_token',
    USER_KEY: 'mfs_user_data',
    ROLES: {
        ADMIN: 'admin',
        OFFICER: 'officer',
        TELLER: 'teller'
    },
    LOAN_STATUS: {
        PENDING: 'pending',
        APPROVED: 'approved',
        DISBURSED: 'disbursed',
        REJECTED: 'rejected',
        CLOSED: 'closed',
        WRITTEN_OFF: 'written_off'
    },
    PAYMENT_METHODS: [
        { value: 'cash', label: 'Cash' },
        { value: 'bank_transfer', label: 'Bank Transfer' },
        { value: 'mobile_money', label: 'Mobile Money' },
        { value: 'check', label: 'Check' },
        { value: 'other', label: 'Other' }
    ]
};

// Helper functions
const formatCurrency = (amount, currency = APP_CONFIG.CURRENCY) => {
    return `${currency}${parseFloat(amount || 0).toLocaleString(undefined, {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    })}`;
};

const formatDate = (dateString, format = APP_CONFIG.DATE_FORMAT) => {
    if (!dateString) return '';
    
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return '';
    
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    
    return format
        .replace('DD', day)
        .replace('MM', month)
        .replace('YYYY', year)
        .replace('HH', hours)
        .replace('mm', minutes);
};

const getStatusBadge = (status) => {
    const statusMap = {
        'active': 'success',
        'inactive': 'secondary',
        'blacklisted': 'danger',
        'pending': 'warning',
        'approved': 'info',
        'disbursed': 'primary',
        'rejected': 'danger',
        'closed': 'success',
        'written_off': 'dark'
    };
    
    const statusText = status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
    const badgeClass = statusMap[status] || 'secondary';
    
    return `<span class="badge bg-${badgeClass}">${statusText}</span>`;
};

// Storage helpers
const storage = {
    set: (key, value) => {
        try {
            localStorage.setItem(key, JSON.stringify(value));
            return true;
        } catch (e) {
            console.error('Error saving to localStorage', e);
            return false;
        }
    },
    get: (key, defaultValue = null) => {
        try {
            const value = localStorage.getItem(key);
            return value ? JSON.parse(value) : defaultValue;
        } catch (e) {
            console.error('Error reading from localStorage', e);
            return defaultValue;
        }
    },
    remove: (key) => {
        try {
            localStorage.removeItem(key);
            return true;
        } catch (e) {
            console.error('Error removing from localStorage', e);
            return false;
        }
    },
    clear: () => {
        try {
            localStorage.clear();
            return true;
        } catch (e) {
            console.error('Error clearing localStorage', e);
            return false;
        }
    }
};

// API request helper
const apiRequest = async (endpoint, method = 'GET', data = null, useAuth = true) => {
    const url = `${API_CONFIG.BASE_URL}${endpoint}`;
    const token = storage.get(APP_CONFIG.TOKEN_KEY);
    
    const headers = useAuth 
        ? API_CONFIG.getAuthHeader(token)
        : API_CONFIG.DEFAULT_HEADERS;
    
    const config = {
        method,
        headers,
        body: data ? JSON.stringify(data) : null
    };
    
    try {
        const response = await fetch(url, config);
        const responseData = await response.json();
        
        if (!response.ok) {
            throw new Error(responseData.message || 'Something went wrong');
        }
        
        return responseData;
    } catch (error) {
        console.error('API Request Error:', error);
        throw error;
    }
};
