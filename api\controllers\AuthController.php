<?php
require_once 'BaseController.php';

class AuthController extends BaseController {
    private $auth;
    
    public function __construct() {
        parent::__construct();
        $this->auth = new Auth();
    }
    
    public function login($data) {
        // Validate required fields
        $required_fields = ['username', 'password'];
        $missing_fields = [];
        
        foreach ($required_fields as $field) {
            if (empty($data[$field])) {
                $missing_fields[] = $field;
            }
        }
        
        if (!empty($missing_fields)) {
            $this->sendResponse(400, [
                'status' => 'error',
                'message' => 'Missing required fields',
                'missing_fields' => $missing_fields
            ]);
            return;
        }
        
        // Attempt login
        $result = $this->auth->login($data['username'], $data['password']);
        
        if ($result['status'] === 'success') {
            $this->sendResponse(200, $result);
        } else {
            $this->sendResponse(401, [
                'status' => 'error',
                'message' => $result['message'] ?? 'Invalid username or password'
            ]);
        }
    }
    
    public function register($data) {
        // Validate required fields
        $required_fields = ['username', 'password', 'email', 'full_name'];
        $missing_fields = [];
        
        foreach ($required_fields as $field) {
            if (empty($data[$field])) {
                $missing_fields[] = $field;
            }
        }
        
        if (!empty($missing_fields)) {
            $this->sendResponse(400, [
                'status' => 'error',
                'message' => 'Missing required fields',
                'missing_fields' => $missing_fields
            ]);
            return;
        }
        
        // Validate email format
        if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            $this->sendResponse(400, [
                'status' => 'error',
                'message' => 'Invalid email format'
            ]);
            return;
        }
        
        // Validate password strength
        if (strlen($data['password']) < 8) {
            $this->sendResponse(400, [
                'status' => 'error',
                'message' => 'Password must be at least 8 characters long'
            ]);
            return;
        }
        
        // Attempt registration
        $result = $this->auth->register([
            'username' => $data['username'],
            'password' => $data['password'],
            'email' => $data['email'],
            'full_name' => $data['full_name']
        ]);
        
        if ($result['status'] === 'success') {
            $this->sendResponse(201, [
                'status' => 'success',
                'message' => 'Registration successful',
                'token' => $result['token'],
                'user' => $result['user']
            ]);
        } else {
            $this->sendResponse(400, [
                'status' => 'error',
                'message' => $result['message'] ?? 'Registration failed'
            ]);
        }
    }
    
    public function getCurrentUser() {
        if (!$this->user_data) {
            $this->sendResponse(401, [
                'status' => 'error',
                'message' => 'Not authenticated'
            ]);
            return;
        }
        
        $this->sendResponse(200, [
            'status' => 'success',
            'data' => $this->user_data
        ]);
    }
}
