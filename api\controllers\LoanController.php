<?php
require_once 'BaseController.php';

class LoanController extends BaseController {
    private $table_name = 'loans';
    
    public function __construct() {
        parent::__construct();
    }
    
    public function getAll() {
        $query = "SELECT l.*, c.first_name, c.last_name, c.client_id as client_number, 
                 p.name as product_name, p.interest_rate as product_interest_rate 
                 FROM " . $this->table_name . " l 
                 JOIN clients c ON l.client_id = c.id 
                 JOIN loan_products p ON l.product_id = p.id 
                 ORDER BY l.created_at DESC";
        
        $stmt = $this->db->prepare($query);
        $stmt->execute();
        
        $loans = [];
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $loans[] = $this->formatLoanData($row);
        }
        
        $this->sendResponse(200, $loans);
    }
    
    public function get($id) {
        $query = "SELECT l.*, c.first_name, c.last_name, c.client_id as client_number, 
                 p.name as product_name, p.interest_rate as product_interest_rate 
                 FROM " . $this->table_name . " l 
                 JOIN clients c ON l.client_id = c.id 
                 JOIN loan_products p ON l.product_id = p.id 
                 WHERE l.id = :id LIMIT 1";
        
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':id', $id);
        $stmt->execute();
        
        if ($stmt->rowCount() > 0) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            $this->sendResponse(200, $this->formatLoanData($row));
        } else {
            $this->sendResponse(404, ['message' => 'Loan not found']);
        }
    }
    
    public function create($data) {
        // Validate required fields
        $required_fields = ['client_id', 'product_id', 'amount', 'term', 'purpose'];
        $this->validateRequiredFields($data, $required_fields);
        
        // Get client and product details
        $client = $this->getClient($data['client_id']);
        $product = $this->getProduct($data['product_id']);
        
        // Validate loan amount against product limits
        if ($data['amount'] < $product['min_amount'] || $data['amount'] > $product['max_amount']) {
            $this->sendResponse(400, [
                'status' => 'error',
                'message' => 'Loan amount must be between $' . $product['min_amount'] . ' and $' . $product['max_amount']
            ]);
        }
        
        // Validate loan term against product terms
        if ($data['term'] < $product['term_min'] || $data['term'] > $product['term_max']) {
            $this->sendResponse(400, [
                'status' => 'error',
                'message' => 'Loan term must be between ' . $product['term_min'] . ' and ' . $product['term_max'] . ' months'
            ]);
        }
        
        // Generate loan number
        $loan_number = 'LN' . date('Ymd') . strtoupper(uniqid());
        
        // Calculate end date
        $start_date = date('Y-m-d');
        $end_date = date('Y-m-d', strtotime("+{$data['term']} months", strtotime($start_date)));
        
        // Insert new loan
        $query = "INSERT INTO " . $this->table_name . " 
                 (loan_number, client_id, product_id, amount, interest_rate, term, 
                 start_date, end_date, status, purpose, notes, created_by)
                 VALUES 
                 (:loan_number, :client_id, :product_id, :amount, :interest_rate, :term, 
                 :start_date, :end_date, 'pending', :purpose, :notes, :created_by)";
        
        $stmt = $this->db->prepare($query);
        
        // Bind parameters
        $stmt->bindParam(':loan_number', $loan_number);
        $stmt->bindParam(':client_id', $data['client_id']);
        $stmt->bindParam(':product_id', $data['product_id']);
        $stmt->bindParam(':amount', $data['amount']);
        $stmt->bindParam(':interest_rate', $product['interest_rate']);
        $stmt->bindParam(':term', $data['term']);
        $stmt->bindParam(':start_date', $start_date);
        $stmt->bindParam(':end_date', $end_date);
        $stmt->bindParam(':purpose', $data['purpose']);
        $stmt->bindParam(':notes', $data['notes'] ?? null);
        $stmt->bindParam(':created_by', $this->user_data['id']);
        
        if ($stmt->execute()) {
            $loan_id = $this->db->lastInsertId();
            $this->logActivity('Created loan application #' . $loan_number);
            
            // Get the newly created loan
            $query = "SELECT l.*, c.first_name, c.last_name, c.client_id as client_number, 
                     p.name as product_name, p.interest_rate as product_interest_rate 
                     FROM " . $this->table_name . " l 
                     JOIN clients c ON l.client_id = c.id 
                     JOIN loan_products p ON l.product_id = p.id 
                     WHERE l.id = :id";
            
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':id', $loan_id);
            $stmt->execute();
            
            $loan = $stmt->fetch(PDO::FETCH_ASSOC);
            
            $this->sendResponse(201, [
                'status' => 'success',
                'message' => 'Loan application submitted successfully',
                'data' => $this->formatLoanData($loan)
            ]);
        } else {
            $this->sendResponse(500, [
                'status' => 'error',
                'message' => 'Failed to submit loan application'
            ]);
        }
    }
    
    public function updateStatus($id, $data) {
        // Check if loan exists
        $loan = $this->getLoan($id);
        
        // Validate status update
        $allowed_statuses = ['approved', 'rejected', 'disbursed', 'closed', 'written_off'];
        if (!isset($data['status']) || !in_array($data['status'], $allowed_statuses)) {
            $this->sendResponse(400, [
                'status' => 'error',
                'message' => 'Invalid status. Must be one of: ' . implode(', ', $allowed_statuses)
            ]);
        }
        
        // Update loan status
        $query = "UPDATE " . $this->table_name . " SET status = :status, updated_at = NOW() WHERE id = :id";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':status', $data['status']);
        $stmt->bindParam(':id', $id);
        
        if ($stmt->execute()) {
            $this->logActivity('Updated loan #' . $loan['loan_number'] . ' status to ' . $data['status']);
            
            // If loan is disbursed, create a transaction
            if ($data['status'] === 'disbursed') {
                $this->recordLoanDisbursement($loan);
            }
            
            $this->sendResponse(200, [
                'status' => 'success',
                'message' => 'Loan status updated successfully'
            ]);
        } else {
            $this->sendResponse(500, [
                'status' => 'error',
                'message' => 'Failed to update loan status'
            ]);
        }
    }
    
    public function recordRepayment($data) {
        // Validate required fields
        $required_fields = ['loan_id', 'amount', 'payment_date', 'payment_method'];
        $this->validateRequiredFields($data, $required_fields);
        
        // Check if loan exists and is active
        $loan = $this->getLoan($data['loan_id']);
        
        if (!in_array($loan['status'], ['disbursed', 'approved'])) {
            $this->sendResponse(400, [
                'status' => 'error',
                'message' => 'Cannot record payment for a loan with status: ' . $loan['status']
            ]);
        }
        
        // Generate payment number
        $payment_number = 'PYT' . date('Ymd') . strtoupper(uniqid());
        
        // Record payment
        $query = "INSERT INTO payments 
                 (payment_number, loan_id, amount, payment_date, payment_method, reference_number, notes, received_by)
                 VALUES 
                 (:payment_number, :loan_id, :amount, :payment_date, :payment_method, :reference_number, :notes, :received_by)";
        
        $stmt = $this->db->prepare($query);
        
        // Bind parameters
        $stmt->bindParam(':payment_number', $payment_number);
        $stmt->bindParam(':loan_id', $data['loan_id']);
        $stmt->bindParam(':amount', $data['amount']);
        $stmt->bindParam(':payment_date', $data['payment_date']);
        $stmt->bindParam(':payment_method', $data['payment_method']);
        $stmt->bindParam(':reference_number', $data['reference_number'] ?? null);
        $stmt->bindParam(':notes', $data['notes'] ?? null);
        $stmt->bindParam(':received_by', $this->user_data['id']);
        
        if ($stmt->execute()) {
            $payment_id = $this->db->lastInsertId();
            $this->logActivity('Recorded payment #' . $payment_number . ' for loan #' . $loan['loan_number']);
            
            // Record transaction
            $this->recordTransaction([
                'type' => 'loan_repayment',
                'loan_id' => $data['loan_id'],
                'amount' => $data['amount'],
                'description' => 'Loan repayment',
                'reference_number' => $payment_number
            ]);
            
            // Check if loan is fully paid
            $this->checkLoanStatus($data['loan_id']);
            
            $this->sendResponse(201, [
                'status' => 'success',
                'message' => 'Payment recorded successfully',
                'payment_id' => $payment_id,
                'payment_number' => $payment_number
            ]);
        } else {
            $this->sendResponse(500, [
                'status' => 'error',
                'message' => 'Failed to record payment'
            ]);
        }
    }
    
    private function getClient($client_id) {
        $query = "SELECT * FROM clients WHERE id = :id LIMIT 1";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':id', $client_id);
        $stmt->execute();
        
        if ($stmt->rowCount() === 0) {
            $this->sendResponse(404, [
                'status' => 'error',
                'message' => 'Client not found'
            ]);
        }
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    private function getProduct($product_id) {
        $query = "SELECT * FROM loan_products WHERE id = :id AND status = 1 LIMIT 1";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':id', $product_id);
        $stmt->execute();
        
        if ($stmt->rowCount() === 0) {
            $this->sendResponse(404, [
                'status' => 'error',
                'message' => 'Loan product not found or inactive'
            ]);
        }
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    private function getLoan($loan_id) {
        $query = "SELECT * FROM " . $this->table_name . " WHERE id = :id LIMIT 1";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':id', $loan_id);
        $stmt->execute();
        
        if ($stmt->rowCount() === 0) {
            $this->sendResponse(404, [
                'status' => 'error',
                'message' => 'Loan not found'
            ]);
        }
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    private function recordLoanDisbursement($loan) {
        // Record transaction for loan disbursement
        $this->recordTransaction([
            'type' => 'loan_disbursement',
            'loan_id' => $loan['id'],
            'amount' => -$loan['amount'], // Negative amount for disbursement
            'description' => 'Loan disbursement',
            'reference_number' => $loan['loan_number']
        ]);
    }
    
    private function recordTransaction($data) {
        // Get current balance
        $balance = $this->getCurrentBalance($data['loan_id']);
        $new_balance = $balance + $data['amount'];
        
        // Generate transaction number
        $transaction_number = 'TXN' . date('Ymd') . strtoupper(uniqid());
        
        // Insert transaction
        $query = "INSERT INTO transactions 
                 (transaction_number, loan_id, type, amount, balance_after, 
                 transaction_date, description, reference_number, created_by)
                 VALUES 
                 (:transaction_number, :loan_id, :type, :amount, :balance_after, 
                 NOW(), :description, :reference_number, :created_by)";
        
        $stmt = $this->db->prepare($query);
        
        $stmt->bindParam(':transaction_number', $transaction_number);
        $stmt->bindParam(':loan_id', $data['loan_id']);
        $stmt->bindParam(':type', $data['type']);
        $stmt->bindParam(':amount', $data['amount']);
        $stmt->bindParam(':balance_after', $new_balance);
        $stmt->bindParam(':description', $data['description']);
        $stmt->bindParam(':reference_number', $data['reference_number']);
        $stmt->bindParam(':created_by', $this->user_data['id']);
        
        return $stmt->execute();
    }
    
    private function getCurrentBalance($loan_id) {
        $query = "SELECT balance_after FROM transactions 
                 WHERE loan_id = :loan_id 
                 ORDER BY id DESC 
                 LIMIT 1";
        
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(':loan_id', $loan_id);
        $stmt->execute();
        
        if ($stmt->rowCount() > 0) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            return (float)$row['balance_after'];
        }
        
        // If no transactions yet, return 0
        return 0;
    }
    
    private function checkLoanStatus($loan_id) {
        // Get loan details
        $loan = $this->getLoan($loan_id);
        $balance = $this->getCurrentBalance($loan_id);
        
        // If balance is 0 or positive, mark loan as closed
        if ($balance >= 0 && $loan['status'] !== 'closed') {
            $query = "UPDATE " . $this->table_name . " SET status = 'closed', updated_at = NOW() WHERE id = :id";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(':id', $loan_id);
            $stmt->execute();
            
            $this->logActivity('Loan #' . $loan['loan_number'] . ' has been fully paid and closed');
        }
    }
    
    private function formatLoanData($loan) {
        $loan['id'] = (int)$loan['id'];
        $loan['client_id'] = (int)$loan['client_id'];
        $loan['product_id'] = (int)$loan['product_id'];
        $loan['amount'] = (float)$loan['amount'];
        $loan['interest_rate'] = (float)$loan['interest_rate'];
        $loan['term'] = (int)$loan['term'];
        $loan['balance'] = $this->getCurrentBalance($loan['id']);
        
        // Calculate next payment due date (simplified)
        $loan['next_payment_date'] = date('Y-m-d', strtotime('+1 month'));
        
        // Calculate total interest
        $monthly_interest_rate = $loan['interest_rate'] / 100 / 12;
        $monthly_payment = ($loan['amount'] * $monthly_interest_rate * pow(1 + $monthly_interest_rate, $loan['term'])) 
                          / (pow(1 + $monthly_interest_rate, $loan['term']) - 1);
        $total_payment = $monthly_payment * $loan['term'];
        $loan['total_interest'] = $total_payment - $loan['amount'];
        $loan['monthly_payment'] = $monthly_payment;
        
        // Add client info
        if (isset($loan['first_name']) && isset($loan['last_name'])) {
            $loan['client_name'] = $loan['first_name'] . ' ' . $loan['last_name'];
            $loan['client_number'] = $loan['client_number'] ?? '';
        }
        
        // Add product info
        if (isset($loan['product_name'])) {
            $loan['product_name'] = $loan['product_name'];
        }
        
        // Remove sensitive or unnecessary fields
        unset($loan['first_name'], $loan['last_name'], $loan['product_interest_rate']);
        
        return $loan;
    }
}
