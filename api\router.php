<?php
// Enable CORS
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS");
header("Access-Control-Max-Age: 3600");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With, X-API-Key");

// Include required files
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../config/jwt.php';
require_once __DIR__ . '/jwt.php';
require_once __DIR__ . '/auth.php';
require_once __DIR__ . '/controllers/BaseController.php';
require_once __DIR__ . '/controllers/AuthController.php';
require_once __DIR__ . '/controllers/LoanController.php';
require_once __DIR__ . '/controllers/ClientController.php';
require_once __DIR__ . '/controllers/DashboardController.php';

// Create a new JWT instance
$jwt = new JWT();

// Get request method and URI
$method = $_SERVER['REQUEST_METHOD'];
$uri = parse_url($_SERVER['REQUEST_URI'], PHP_URL_PATH);
$uri = explode('/', trim($uri, '/'));

// Remove 'sub system' and 'api' from the URI if present
$filtered_uri = [];
$skip_next = false;
foreach ($uri as $part) {
    if ($part === 'sub' || $part === 'sub%20system') {
        $skip_next = true;
        continue;
    }
    if ($skip_next && $part === 'system') {
        $skip_next = false;
        continue;
    }
    if ($part === 'api') {
        continue;
    }
    if (!empty($part)) {
        $filtered_uri[] = $part;
    }
}
$uri = $filtered_uri;

// Define routes
$routes = [
    // Auth routes (public)
    'POST auth/register' => ['controller' => 'AuthController', 'method' => 'register', 'auth' => false],
    'POST auth/login' => ['controller' => 'AuthController', 'method' => 'login', 'auth' => false],
    'POST auth/refresh-token' => ['controller' => 'AuthController', 'method' => 'refreshToken', 'auth' => false],
    'POST auth/forgot-password' => ['controller' => 'AuthController', 'method' => 'forgotPassword', 'auth' => false],
    'POST auth/reset-password' => ['controller' => 'AuthController', 'method' => 'resetPassword', 'auth' => false],
    
    // Dashboard routes (protected)
    'GET dashboard/stats' => ['controller' => 'DashboardController', 'method' => 'getStats', 'auth' => true],
    
    // Client routes (protected)
    'GET clients' => ['controller' => 'ClientController', 'method' => 'index', 'auth' => true],
    'GET clients/:id' => ['controller' => 'ClientController', 'method' => 'show', 'auth' => true],
    'POST clients' => ['controller' => 'ClientController', 'method' => 'store', 'auth' => true],
    'PUT clients/:id' => ['controller' => 'ClientController', 'method' => 'update', 'auth' => true],
    'DELETE clients/:id' => ['controller' => 'ClientController', 'method' => 'delete', 'auth' => true],
    'POST clients/:id/photo' => ['controller' => 'ClientController', 'method' => 'uploadPhoto', 'auth' => true],
    'GET clients/:id/loans' => ['controller' => 'ClientController', 'method' => 'getClientLoans', 'auth' => true],
    'GET clients/:id/transactions' => ['controller' => 'ClientController', 'method' => 'getClientTransactions', 'auth' => true],
    
    // Loan routes (protected)
    'GET loans' => ['controller' => 'LoanController', 'method' => 'index', 'auth' => true],
    'GET loans/:id' => ['controller' => 'LoanController', 'method' => 'show', 'auth' => true],
    'POST loans' => ['controller' => 'LoanController', 'method' => 'store', 'auth' => true],
    'PUT loans/:id' => ['controller' => 'LoanController', 'method' => 'update', 'auth' => true],
    'DELETE loans/:id' => ['controller' => 'LoanController', 'method' => 'delete', 'auth' => true],
    'POST loans/:id/approve' => ['controller' => 'LoanController', 'method' => 'approve', 'auth' => true],
    'POST loans/:id/reject' => ['controller' => 'LoanController', 'method' => 'reject', 'auth' => true],
    'POST loans/:id/disburse' => ['controller' => 'LoanController', 'method' => 'disburse', 'auth' => true],
    'POST loans/:id/write-off' => ['controller' => 'LoanController', 'method' => 'writeOff', 'auth' => true],
    'POST loans/:id/close' => ['controller' => 'LoanController', 'method' => 'close', 'auth' => true],
    'GET loans/:id/payments' => ['controller' => 'LoanController', 'method' => 'getPayments', 'auth' => true],
    'POST loans/:id/payments' => ['controller' => 'LoanController', 'method' => 'recordPayment', 'auth' => true],
    'GET loans/:id/schedule' => ['controller' => 'LoanController', 'method' => 'getPaymentSchedule', 'auth' => true],
    
    // Report routes (protected)
    'GET reports/generate' => ['controller' => 'ReportController', 'method' => 'generateReport', 'auth' => true],
    'GET reports/loans' => ['controller' => 'ReportController', 'method' => 'generateReport', 'auth' => true],
    'GET reports/payments' => ['controller' => 'ReportController', 'method' => 'generateReport', 'auth' => true],
    'GET reports/clients' => ['controller' => 'ReportController', 'method' => 'generateReport', 'auth' => true],
    'GET reports/collection' => ['controller' => 'ReportController', 'method' => 'generateReport', 'auth' => true],
    'GET reports/portfolio' => ['controller' => 'ReportController', 'method' => 'generateReport', 'auth' => true],
];

// Helper function to match routes with parameters
function matchRoute($uri, $routeParts) {
    $uriParts = $uri;

    if (count($routeParts) !== count($uriParts)) {
        return false;
    }

    $params = [];

    foreach ($routeParts as $i => $part) {
        if (strpos($part, ':') === 0) {
            $params[substr($part, 1)] = $uriParts[$i];
        } elseif ($part !== $uriParts[$i]) {
            return false;
        }
    }

    return ['params' => $params];
}

// Find matching route
$matchedRoute = null;
$routeParams = [];

foreach ($routes as $route => $handler) {
    $routeParts = explode(' ', $route, 2);
    $routeMethod = $routeParts[0];
    $routePath = $routeParts[1] ?? '';
    
    if ($routeMethod !== $method) {
        continue;
    }
    
    $routePathParts = explode('/', trim($routePath, '/'));
    $match = matchRoute($uri, $routePathParts);
    
    if ($match !== false) {
        $matchedRoute = $handler;
        $routeParams = $match['params'];
        break;
    }
}

// Handle 404 if no route matched
if (!$matchedRoute) {
    header("HTTP/1.1 404 Not Found");
    echo json_encode(['error' => 'Not Found']);
    exit();
}

// Check authentication if required
if ($matchedRoute['auth']) {
    $token = JWT::getTokenFromHeader();

    if (!$token) {
        header('Content-Type: application/json');
        http_response_code(401);
        echo json_encode([
            'status' => 'error',
            'message' => 'No token provided'
        ]);
        exit();
    }

    try {
        $decoded = JWT::decode($token);
        // Store user data for use in controllers
        $_SESSION['user'] = $decoded;
    } catch (Exception $e) {
        header('Content-Type: application/json');
        http_response_code(401);
        echo json_encode([
            'status' => 'error',
            'message' => 'Invalid token: ' . $e->getMessage()
        ]);
        exit();
    }
}

// Get the controller and method
$controllerName = $matchedRoute['controller'];
$methodName = $matchedRoute['method'];

// Create controller instance
$controller = new $controllerName();

// Get request data
$requestData = [];
if ($method === 'POST' || $method === 'PUT' || $method === 'PATCH') {
    $contentType = isset($_SERVER['CONTENT_TYPE']) ? $_SERVER['CONTENT_TYPE'] : '';
    
    if (strpos($contentType, 'application/json') !== false) {
        $requestData = json_decode(file_get_contents('php://input'), true);
    } else {
        $requestData = $_POST;
        // Handle file uploads
        if (!empty($_FILES)) {
            $requestData['_files'] = $_FILES;
        }
    }
}

// Add route parameters to request data
$requestData = array_merge($requestData, $routeParams);

// Add query parameters
if (!empty($_GET)) {
    $requestData = array_merge($requestData, $_GET);
}

// Set request data in the controller if it has a setRequest method
if (method_exists($controller, 'setRequest')) {
    $controller->setRequest($requestData);
}

// Call the controller method
try {
    // Check if the method exists in the controller
    if (!method_exists($controller, $methodName)) {
        throw new Exception("Method {$methodName} not found in controller {$controllerName}");
    }

    // Call the method with the request data
    if (in_array($methodName, ['login', 'register'])) {
        // For auth methods, pass the request data as parameter
        $response = $controller->$methodName($requestData);
    } else {
        // For other methods, use route parameters
        $response = call_user_func_array([$controller, $methodName], array_values($routeParams));
    }

    // If the controller didn't send a response, send a default one
    if ($response !== null) {
        header('Content-Type: application/json');
        echo json_encode($response);
    }

} catch (Exception $e) {
    // Handle exceptions
    header('Content-Type: application/json');
    http_response_code(500);
    echo json_encode([
        'error' => $e->getMessage(),
        'trace' => $e->getTraceAsString()
    ]);
}
