# Enable URL rewriting
RewriteEngine On

# Set the base directory
RewriteBase /sub%20system/api/

# Enable CORS
Header always set Access-Control-Allow-Origin "*"
Header always set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
Header always set Access-Control-Allow-Headers "Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With, X-API-Key"

# Handle preflight requests
RewriteCond %{REQUEST_METHOD} OPTIONS
RewriteRule ^(.*)$ index.php [R=200,L]

# If the request is for a file that exists, serve it directly
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d

# Route all requests to index.php
RewriteRule ^(.*)$ index.php [QSA,L]

# Set default character set
AddDefaultCharset UTF-8

# Disable directory listing
Options -Indexes
