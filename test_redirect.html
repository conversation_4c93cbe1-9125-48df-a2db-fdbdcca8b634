<!DOCTYPE html>
<html>
<head>
    <title>Test Redirect</title>
</head>
<body>
    <h1>Testing Redirect</h1>
    <p>This page tests if redirect is working.</p>
    
    <button onclick="testRedirect()">Test Redirect to Dashboard</button>
    <button onclick="testLogin()">Test Login Process</button>
    <button onclick="checkStorage()">Check Storage</button>
    
    <div id="output"></div>
    
    <script>
        function log(message) {
            document.getElementById('output').innerHTML += '<p>' + message + '</p>';
            console.log(message);
        }
        
        function testRedirect() {
            log('Testing redirect...');
            window.location.href = 'index.html';
        }
        
        async function testLogin() {
            log('Testing login process...');
            
            try {
                const response = await fetch('api/login.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ username: 'admin', password: 'password' })
                });
                
                const data = await response.json();
                log('Login response: ' + JSON.stringify(data));
                
                if (data.data && data.data.token) {
                    localStorage.setItem('mfs_auth_token', data.data.token);
                    localStorage.setItem('mfs_user_data', JSON.stringify(data.data.user));
                    log('Token stored: ' + data.data.token);
                    
                    setTimeout(() => {
                        log('Redirecting...');
                        window.location.href = 'index.html';
                    }, 1000);
                }
                
            } catch (error) {
                log('Login error: ' + error.message);
            }
        }
        
        function checkStorage() {
            const token = localStorage.getItem('mfs_auth_token');
            const user = localStorage.getItem('mfs_user_data');
            
            log('Current token: ' + token);
            log('Current user: ' + user);
            
            if (token) {
                log('Token exists - should be able to access dashboard');
            } else {
                log('No token - will be redirected to login');
            }
        }
    </script>
</body>
</html>
