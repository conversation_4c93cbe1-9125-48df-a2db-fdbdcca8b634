<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Login - Microfinance Management System</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .debug { background: #f0f0f0; padding: 10px; margin: 10px 0; border-radius: 5px; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
        input, button { padding: 10px; margin: 5px; }
        button { background: #007bff; color: white; border: none; cursor: pointer; }
    </style>
</head>
<body>
    <h1>Debug Login Test</h1>
    
    <form id="debugLoginForm">
        <div>
            <input type="text" id="username" placeholder="Username" value="admin" required>
        </div>
        <div>
            <input type="password" id="password" placeholder="Password" value="password" required>
        </div>
        <div>
            <button type="submit">Test Login</button>
        </div>
    </form>
    
    <div id="debugOutput"></div>
    
    <script>
        function log(message, type = 'info') {
            const output = document.getElementById('debugOutput');
            const div = document.createElement('div');
            div.className = `debug ${type}`;
            div.innerHTML = `<strong>${new Date().toLocaleTimeString()}:</strong> ${message}`;
            output.appendChild(div);
            console.log(message);
        }
        
        document.getElementById('debugLoginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            log('Starting login test...');
            log(`Username: ${username}`);
            log(`Password: ${password ? '***' : 'empty'}`);
            
            try {
                const url = 'api/auth/login';
                log(`Making request to: ${url}`);
                
                const requestData = {
                    username: username,
                    password: password
                };
                log(`Request data: ${JSON.stringify(requestData)}`);
                
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });
                
                log(`Response status: ${response.status} ${response.statusText}`);
                log(`Response headers: ${JSON.stringify([...response.headers.entries()])}`);
                
                const responseText = await response.text();
                log(`Raw response: ${responseText}`);
                
                let data;
                try {
                    data = JSON.parse(responseText);
                    log(`Parsed response: ${JSON.stringify(data, null, 2)}`);
                } catch (parseError) {
                    log(`JSON parse error: ${parseError.message}`, 'error');
                    return;
                }
                
                if (!response.ok) {
                    log(`Request failed: ${data.message || 'Unknown error'}`, 'error');
                    return;
                }
                
                if (data.data && data.data.token) {
                    log('Login successful!', 'success');
                    log(`Token: ${data.data.token.substring(0, 50)}...`);
                    log(`User: ${JSON.stringify(data.data.user)}`);
                    
                    // Store in localStorage
                    localStorage.setItem('mfs_auth_token', JSON.stringify(data.data.token));
                    localStorage.setItem('mfs_user_data', JSON.stringify(data.data.user));
                    
                    log('Data stored in localStorage', 'success');
                    
                    // Check if data was stored correctly
                    const storedToken = localStorage.getItem('mfs_auth_token');
                    const storedUser = localStorage.getItem('mfs_user_data');
                    log(`Stored token: ${storedToken ? 'YES' : 'NO'}`);
                    log(`Stored user: ${storedUser ? 'YES' : 'NO'}`);
                    
                    log('Redirecting to dashboard in 3 seconds...', 'success');
                    setTimeout(() => {
                        window.location.href = 'index.html';
                    }, 3000);
                } else {
                    log('Login response missing token or user data', 'error');
                    log(`Response structure: ${JSON.stringify(Object.keys(data))}`);
                }
                
            } catch (error) {
                log(`Network error: ${error.message}`, 'error');
                log(`Error details: ${error.stack}`, 'error');
            }
        });
        
        // Check current localStorage state
        window.addEventListener('load', function() {
            log('=== Current localStorage state ===');
            const token = localStorage.getItem('mfs_auth_token');
            const user = localStorage.getItem('mfs_user_data');
            log(`Token in storage: ${token ? 'YES' : 'NO'}`);
            log(`User in storage: ${user ? 'YES' : 'NO'}`);
            if (token) log(`Token value: ${token.substring(0, 50)}...`);
            if (user) log(`User value: ${user}`);
        });
    </script>
</body>
</html>
