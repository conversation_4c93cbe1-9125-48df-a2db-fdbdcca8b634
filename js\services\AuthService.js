class AuthService {
    constructor() {
        this.apiService = apiService;
        this.isAuthenticated = this.checkAuth();
        this.user = this.getCurrentUser();
        this.authListeners = [];
    }

    // Check if user is authenticated
    checkAuth() {
        const token = storage.get(APP_CONFIG.TOKEN_KEY);
        return !!token;
    }

    // Get current user from storage
    getCurrentUser() {
        return storage.get(APP_CONFIG.USER_KEY);
    }

    // Add auth state change listener
    addAuthListener(callback) {
        this.authListeners.push(callback);
        return () => {
            this.authListeners = this.authListeners.filter(listener => listener !== callback);
        };
    }

    // Notify all listeners about auth state change
    notifyAuthStateChange() {
        this.isAuthenticated = this.checkAuth();
        this.user = this.getCurrentUser();
        this.authListeners.forEach(listener => listener(this.isAuthenticated, this.user));
    }

    // Login user
    async login(credentials) {
        try {
            const data = await this.apiService.login(credentials);
            this.notifyAuthStateChange();
            return { success: true, data };
        } catch (error) {
            console.error('Login failed:', error);
            return { 
                success: false, 
                error: error.message || 'Login failed. Please check your credentials.'
            };
        }
    }

    // Register new user
    async register(userData) {
        try {
            const data = await this.apiService.register(userData);
            this.notifyAuthStateChange();
            return { success: true, data };
        } catch (error) {
            console.error('Registration failed:', error);
            return {
                success: false,
                error: error.message || 'Registration failed. Please try again.'
            };
        }
    }

    // Logout user
    logout() {
        this.apiService.clearToken();
        storage.remove(APP_CONFIG.USER_KEY);
        this.notifyAuthStateChange();
        window.location.href = '/login.html';
    }

    // Check if user has required role
    hasRole(requiredRole) {
        if (!this.user) return false;
        if (this.user.role === APP_CONFIG.ROLES.ADMIN) return true;
        return this.user.role === requiredRole;
    }

    // Check if user has any of the required roles
    hasAnyRole(requiredRoles) {
        if (!this.user) return false;
        if (this.user.role === APP_CONFIG.ROLES.ADMIN) return true;
        return requiredRoles.includes(this.user.role);
    }

    // Get user permissions based on role
    getPermissions() {
        if (!this.user) return [];
        
        const rolePermissions = {
            [APP_CONFIG.ROLES.ADMIN]: [
                'view_dashboard', 'manage_users', 'manage_clients', 'manage_loans',
                'approve_loans', 'process_payments', 'generate_reports', 'manage_settings'
            ],
            [APP_CONFIG.ROLES.OFFICER]: [
                'view_dashboard', 'manage_clients', 'manage_loans', 'process_payments'
            ],
            [APP_CONFIG.ROLES.TELLER]: [
                'view_dashboard', 'process_payments'
            ]
        };

        return rolePermissions[this.user.role] || [];
    }

    // Check if user has specific permission
    hasPermission(permission) {
        const permissions = this.getPermissions();
        return permissions.includes(permission);
    }

    // Refresh user data
    async refreshUserData() {
        try {
            const data = await this.apiService.getCurrentUser();
            if (data && data.user) {
                storage.set(APP_CONFIG.USER_KEY, data.user);
                this.user = data.user;
                this.notifyAuthStateChange();
                return true;
            }
            return false;
        } catch (error) {
            console.error('Failed to refresh user data:', error);
            return false;
        }
    }
}

// Create a singleton instance
const authService = new AuthService();

export default authService;
