<?php
require_once __DIR__ . '/../config/jwt.php';

class JwtHandler {
    private $secret_key;
    private $algorithm = 'HS256';
    private $issuer = 'microfinance_api';
    private $expire_after = 24 * 60 * 60; // 24 hours in seconds

    public function __construct() {
        $this->secret_key = $this->getSecretKey();
    }

    private function getSecretKey() {
        // Load from environment
        $this->loadEnv();
        return getenv('JWT_SECRET') ?: 'your-secret-key-here-1234567890';
    }

    private function loadEnv() {
        if (!getenv('JWT_SECRET')) {
            $envFile = __DIR__ . '/../.env';
            if (file_exists($envFile)) {
                $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
                foreach ($lines as $line) {
                    if (strpos(trim($line), '#') === 0) {
                        continue;
                    }

                    if (strpos($line, '=') !== false) {
                        list($name, $value) = explode('=', $line, 2);
                        $name = trim($name);
                        $value = trim($value);

                        if (!array_key_exists($name, $_ENV)) {
                            putenv("$name=$value");
                            $_ENV[$name] = $value;
                            $_SERVER[$name] = $value;
                        }
                    }
                }
            }
        }
    }
    
    public function generateJwtToken($payload) {
        $header = json_encode(['typ' => 'JWT', 'alg' => $this->algorithm]);
        $current_time = time();
        
        $token_payload = [
            'iss' => $this->issuer,
            'iat' => $current_time,
            'exp' => $current_time + $this->expire_after,
            'data' => $payload
        ];
        
        $base64url_header = $this->base64url_encode($header);
        $base64url_payload = $this->base64url_encode(json_encode($token_payload));
        
        $signature = hash_hmac('sha256', "$base64url_header.$base64url_payload", $this->secret_key, true);
        $base64url_signature = $this->base64url_encode($signature);
        
        return "$base64url_header.$base64url_payload.$base64url_signature";
    }
    
    public function validateToken($token) {
        $token_parts = explode('.', $token);
        
        if (count($token_parts) !== 3) {
            throw new Exception('Invalid token format');
        }
        
        list($header, $payload, $signature) = $token_parts;
        
        // Verify signature
        $signature_check = $this->base64url_encode(
            hash_hmac('sha256', "$header.$payload", $this->secret_key, true)
        );
        
        if ($signature_check !== $signature) {
            throw new Exception('Invalid token signature');
        }
        
        $decoded_payload = json_decode($this->base64url_decode($payload), true);
        
        // Check token expiration
        if (isset($decoded_payload['exp']) && $decoded_payload['exp'] < time()) {
            throw new Exception('Token has expired');
        }
        
        // Check token issuer
        if (isset($decoded_payload['iss']) && $decoded_payload['iss'] !== $this->issuer) {
            throw new Exception('Invalid token issuer');
        }
        
        return $decoded_payload['data'];
    }
    
    private function base64url_encode($data) {
        return rtrim(strtr(base64_encode($data), '+/', '-_'), '=');
    }
    
    private function base64url_decode($data) {
        return base64_decode(str_pad(strtr($data, '-_', '+/'), strlen($data) % 4, '=', STR_PAD_RIGHT));
    }
}
