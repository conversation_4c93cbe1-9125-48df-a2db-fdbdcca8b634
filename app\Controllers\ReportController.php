<?php

require_once __DIR__ . '/../../vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Dompdf\Dompdf;

class ReportController extends BaseController {
    public function generateReport() {
        $data = $this->request->getParsedBody();
        
        // Validate input
        $validation = $this->validate([
            'report_type' => 'required|in:loans,payments,clients,collection,portfolio',
            'format' => 'required|in:pdf,excel,csv,json',
            'start_date' => 'date',
            'end_date' => 'date',
            'status' => 'string',
            'loan_product_id' => 'numeric',
            'officer_id' => 'numeric'
        ]);
        
        if ($validation !== true) {
            return $validation;
        }
        
        $reportType = $data['report_type'];
        $format = $data['format'];
        $startDate = $data['start_date'] ?? null;
        $endDate = $data['end_date'] ?? null;
        
        try {
            // Generate report data based on type
            $reportData = $this->generateReportData($reportType, $startDate, $endDate, $data);
            
            // Format the report based on requested format
            switch ($format) {
                case 'pdf':
                    return $this->generatePdf($reportData, $reportType);
                case 'excel':
                    return $this->generateExcel($reportData, $reportType);
                case 'csv':
                    return $this->generateCsv($reportData, $reportType);
                case 'json':
                default:
                    return $this->success($reportData);
            }
            
        } catch (Exception $e) {
            return $this->error('Failed to generate report: ' . $e->getMessage());
        }
    }
    
    private function generateReportData($type, $startDate, $endDate, $filters) {
        $data = [
            'report_type' => $type,
            'title' => ucfirst($type) . ' Report',
            'generated_at' => date('Y-m-d H:i:s'),
            'filters' => $this->getAppliedFilters($filters),
            'data' => []
        ];
        
        switch ($type) {
            case 'loans':
                $data['data'] = $this->getLoansReportData($startDate, $endDate, $filters);
                break;
                
            case 'payments':
                $data['data'] = $this->getPaymentsReportData($startDate, $endDate, $filters);
                break;
                
            case 'clients':
                $data['data'] = $this->getClientsReportData($filters);
                break;
                
            case 'collection':
                $data['data'] = $this->getCollectionReportData($startDate, $endDate, $filters);
                break;
                
            case 'portfolio':
                $data['data'] = $this->getPortfolioReportData($filters);
                break;
        }
        
        // Add summary stats if needed
        if (in_array($type, ['loans', 'payments', 'collection'])) {
            $data['summary'] = $this->calculateSummary($data['data'], $type);
        }
        
        return $data;
    }
    
    private function getLoansReportData($startDate, $endDate, $filters) {
        $query = "
            SELECT 
                l.*,
                lp.name as product_name,
                CONCAT(c.first_name, ' ', c.last_name) as client_name,
                c.phone,
                (SELECT COALESCE(SUM(amount), 0) FROM payments WHERE loan_id = l.id) as total_paid,
                (l.total_payable - (SELECT COALESCE(SUM(amount), 0) FROM payments WHERE loan_id = l.id)) as outstanding_balance
            FROM loans l
            JOIN loan_products lp ON l.product_id = lp.id
            JOIN clients c ON l.client_id = c.id
            WHERE 1=1
        ";
        
        $params = [];
        
        // Apply date filters
        if ($startDate) {
            $query .= " AND l.application_date >= ?";
            $params[] = $startDate;
        }
        
        if ($endDate) {
            $query .= " AND l.application_date <= ?";
            $params[] = $endDate . ' 23:59:59';
        }
        
        // Apply status filter
        if (!empty($filters['status'])) {
            $query .= " AND l.status = ?";
            $params[] = $filters['status'];
        }
        
        // Apply loan product filter
        if (!empty($filters['loan_product_id'])) {
            $query .= " AND l.product_id = ?";
            $params[] = $filters['loan_product_id'];
        }
        
        // Apply officer filter
        if (!empty($filters['officer_id'])) {
            $query .= " AND l.created_by = ?";
            $params[] = $filters['officer_id'];
        }
        
        $query .= " ORDER BY l.application_date DESC";
        
        $stmt = $this->db->prepare($query);
        $stmt->execute($params);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    private function getPaymentsReportData($startDate, $endDate, $filters) {
        $query = "
            SELECT 
                p.*,
                l.loan_number,
                lp.name as product_name,
                CONCAT(c.first_name, ' ', c.last_name) as client_name,
                c.phone,
                u.username as received_by_name
            FROM payments p
            JOIN loans l ON p.loan_id = l.id
            JOIN loan_products lp ON l.product_id = lp.id
            JOIN clients c ON l.client_id = c.id
            LEFT JOIN users u ON p.received_by = u.id
            WHERE 1=1
        ";
        
        $params = [];
        
        // Apply date filters
        if ($startDate) {
            $query .= " AND p.payment_date >= ?";
            $params[] = $startDate;
        }
        
        if ($endDate) {
            $query .= " AND p.payment_date <= ?";
            $params[] = $endDate . ' 23:59:59';
        }
        
        // Apply payment method filter
        if (!empty($filters['payment_method'])) {
            $query .= " AND p.payment_method = ?";
            $params[] = $filters['payment_method'];
        }
        
        // Apply loan product filter
        if (!empty($filters['loan_product_id'])) {
            $query .= " AND l.product_id = ?";
            $params[] = $filters['loan_product_id'];
        }
        
        // Apply officer filter
        if (!empty($filters['officer_id'])) {
            $query .= " AND p.received_by = ?";
            $params[] = $filters['officer_id'];
        }
        
        $query .= " ORDER BY p.payment_date DESC, p.created_at DESC";
        
        $stmt = $this->db->prepare($query);
        $stmt->execute($params);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    private function getClientsReportData($filters) {
        $query = "
            SELECT 
                c.*,
                (SELECT COUNT(*) FROM loans WHERE client_id = c.id) as total_loans,
                (SELECT COALESCE(SUM(amount), 0) FROM loans WHERE client_id = c.id) as total_borrowed,
                (SELECT COALESCE(SUM(p.amount), 0) 
                 FROM payments p 
                 JOIN loans l ON p.loan_id = l.id 
                 WHERE l.client_id = c.id) as total_repaid
            FROM clients c
            WHERE 1=1
        ";
        
        $params = [];
        
        // Apply status filter
        if (!empty($filters['status'])) {
            $query .= " AND c.status = ?";
            $params[] = $filters['status'];
        }
        
        // Apply registration date filter
        if (!empty($filters['start_date'])) {
            $query .= " AND c.created_at >= ?";
            $params[] = $filters['start_date'];
        }
        
        if (!empty($filters['end_date'])) {
            $query .= " AND c.created_at <= ?";
            $params[] = $filters['end_date'] . ' 23:59:59';
        }
        
        // Apply officer filter
        if (!empty($filters['officer_id'])) {
            $query .= " AND c.created_by = ?";
            $params[] = $filters['officer_id'];
        }
        
        $query .= " ORDER BY c.created_at DESC";
        
        $stmt = $this->db->prepare($query);
        $stmt->execute($params);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    private function getCollectionReportData($startDate, $endDate, $filters) {
        // Get collection data grouped by date and payment method
        $query = "
            SELECT 
                DATE(p.payment_date) as collection_date,
                p.payment_method,
                COUNT(DISTINCT p.loan_id) as loan_count,
                COUNT(p.id) as payment_count,
                SUM(p.amount) as total_collected,
                GROUP_CONCAT(DISTINCT u.username) as collected_by
            FROM payments p
            JOIN loans l ON p.loan_id = l.id
            LEFT JOIN users u ON p.received_by = u.id
            WHERE 1=1
        ";
        
        $params = [];
        
        // Apply date filters
        if ($startDate) {
            $query .= " AND p.payment_date >= ?";
            $params[] = $startDate;
        }
        
        if ($endDate) {
            $query .= " AND p.payment_date <= ?";
            $params[] = $endDate . ' 23:59:59';
        }
        
        // Apply payment method filter
        if (!empty($filters['payment_method'])) {
            $query .= " AND p.payment_method = ?";
            $params[] = $filters['payment_method'];
        }
        
        // Apply loan product filter
        if (!empty($filters['loan_product_id'])) {
            $query .= " AND l.product_id = ?";
            $params[] = $filters['loan_product_id'];
        }
        
        // Apply officer filter
        if (!empty($filters['officer_id'])) {
            $query .= " AND p.received_by = ?";
            $params[] = $filters['officer_id'];
        }
        
        $query .= " GROUP BY DATE(p.payment_date), p.payment_method";
        $query .= " ORDER BY p.payment_date DESC, p.payment_method";
        
        $stmt = $this->db->prepare($query);
        $stmt->execute($params);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    private function getPortfolioReportData($filters) {
        // Get portfolio at risk data
        $query = "
            SELECT 
                lp.id as product_id,
                lp.name as product_name,
                COUNT(DISTINCT l.id) as total_loans,
                COUNT(DISTINCT l.client_id) as total_clients,
                COALESCE(SUM(l.amount), 0) as total_disbursed,
                COALESCE(SUM(l.total_payable), 0) as total_payable,
                COALESCE(SUM(p.total_paid), 0) as total_paid,
                COALESCE(SUM(
                    CASE 
                        WHEN l.next_payment_date < CURDATE() THEN l.monthly_payment
                        ELSE 0 
                    END
                ), 0) as total_overdue,
                COUNT(DISTINCT CASE 
                    WHEN l.next_payment_date < DATE_SUB(CURDATE(), INTERVAL 30 DAY) 
                    THEN l.id 
                END) as loans_30_days_overdue,
                COUNT(DISTINCT CASE 
                    WHEN l.next_payment_date < DATE_SUB(CURDATE(), INTERVAL 60 DAY) 
                    THEN l.id 
                END) as loans_60_days_overdue,
                COUNT(DISTINCT CASE 
                    WHEN l.next_payment_date < DATE_SUB(CURDATE(), INTERVAL 90 DAY) 
                    THEN l.id 
                END) as loans_90_days_overdue
            FROM loan_products lp
            LEFT JOIN loans l ON lp.id = l.product_id AND l.status = 'disbursed'
            LEFT JOIN (
                SELECT loan_id, SUM(amount) as total_paid 
                FROM payments 
                GROUP BY loan_id
            ) p ON l.id = p.loan_id
            WHERE 1=1
        ";
        
        $params = [];
        
        // Apply loan product filter
        if (!empty($filters['loan_product_id'])) {
            $query .= " AND lp.id = ?";
            $params[] = $filters['loan_product_id'];
        }
        
        $query .= " GROUP BY lp.id, lp.name";
        $query .= " ORDER BY lp.name";
        
        $stmt = $this->db->prepare($query);
        $stmt->execute($params);
        
        $portfolioData = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Calculate additional metrics
        foreach ($portfolioData as &$row) {
            $outstanding = $row['total_payable'] - $row['total_paid'];
            $row['outstanding_balance'] = $outstanding;
            $row['par_30'] = $row['loans_30_days_overdue'] > 0 ? 
                ($row['loans_30_days_overdue'] / $row['total_loans']) * 100 : 0;
            $row['par_60'] = $row['loans_60_days_overdue'] > 0 ? 
                ($row['loans_60_days_overdue'] / $row['total_loans']) * 100 : 0;
            $row['par_90'] = $row['loans_90_days_overdue'] > 0 ? 
                ($row['loans_90_days_overdue'] / $row['total_loans']) * 100 : 0;
            
            // Clean up numeric values
            $row['total_disbursed'] = (float) $row['total_disbursed'];
            $row['total_payable'] = (float) $row['total_payable'];
            $row['total_paid'] = (float) $row['total_paid'];
            $row['outstanding_balance'] = (float) $outstanding;
            $row['total_overdue'] = (float) $row['total_overdue'];
            $row['par_30'] = round((float) $row['par_30'], 2);
            $row['par_60'] = round((float) $row['par_60'], 2);
            $row['par_90'] = round((float) $row['par_90'], 2);
        }
        
        return $portfolioData;
    }
    
    private function calculateSummary($data, $reportType) {
        $summary = [];
        
        switch ($reportType) {
            case 'loans':
                $summary['total_loans'] = count($data);
                $summary['total_amount'] = array_sum(array_column($data, 'amount'));
                $summary['total_payable'] = array_sum(array_column($data, 'total_payable'));
                $summary['total_paid'] = array_sum(array_column($data, 'total_paid'));
                $summary['total_outstanding'] = array_sum(array_column($data, 'outstanding_balance'));
                
                // Group by status
                $statusCounts = [];
                foreach ($data as $loan) {
                    $status = strtolower($loan['status']);
                    if (!isset($statusCounts[$status])) {
                        $statusCounts[$status] = 0;
                    }
                    $statusCounts[$status]++;
                }
                $summary['status_summary'] = $statusCounts;
                break;
                
            case 'payments':
                $summary['total_payments'] = count($data);
                $summary['total_amount'] = array_sum(array_column($data, 'amount'));
                
                // Group by payment method
                $methodCounts = [];
                $methodAmounts = [];
                foreach ($data as $payment) {
                    $method = $payment['payment_method'];
                    if (!isset($methodCounts[$method])) {
                        $methodCounts[$method] = 0;
                        $methodAmounts[$method] = 0;
                    }
                    $methodCounts[$method]++;
                    $methodAmounts[$method] += $payment['amount'];
                }
                $summary['method_summary'] = [
                    'counts' => $methodCounts,
                    'amounts' => $methodAmounts
                ];
                break;
                
            case 'collection':
                $summary['total_collected'] = array_sum(array_column($data, 'total_collected'));
                $summary['total_payments'] = array_sum(array_column($data, 'payment_count'));
                $summary['unique_loans'] = array_sum(array_column($data, 'loan_count'));
                
                // Group by payment method
                $methodAmounts = [];
                foreach ($data as $row) {
                    $method = $row['payment_method'];
                    if (!isset($methodAmounts[$method])) {
                        $methodAmounts[$method] = 0;
                    }
                    $methodAmounts[$method] += $row['total_collected'];
                }
                $summary['method_summary'] = $methodAmounts;
                break;
        }
        
        return $summary;
    }
    
    private function getAppliedFilters($filters) {
        $applied = [];
        $validFilters = [
            'start_date', 'end_date', 'status', 'loan_product_id', 'officer_id', 
            'payment_method', 'client_status'
        ];
        
        foreach ($validFilters as $filter) {
            if (isset($filters[$filter]) && $filters[$filter] !== '') {
                $applied[$filter] = $filters[$filter];
            }
        }
        
        return $applied;
    }
    
    private function generatePdf($data, $reportType) {
        $dompdf = new Dompdf();
        $html = $this->getPdfHtml($data, $reportType);
        
        $dompdf->loadHtml($html);
        $dompdf->setPaper('A4', 'landscape');
        $dompdf->render();
        
        $filename = $reportType . '_report_' . date('Ymd_His') . '.pdf';
        
        // Return the PDF as a download
        $output = $dompdf->output();
        
        return $this->response
            ->withHeader('Content-Type', 'application/pdf')
            ->withHeader('Content-Disposition', 'attachment; filename="' . $filename . '"')
            ->withHeader('Content-Length', strlen($output))
            ->write($output);
    }
    
    private function generateExcel($data, $reportType) {
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        $sheet->setTitle(ucfirst($reportType) . ' Report');
        
        // Set headers based on report type
        $headers = $this->getExcelHeaders($reportType);
        $sheet->fromArray([$headers], null, 'A1');
        
        // Add data rows
        $rowData = [];
        foreach ($data['data'] as $item) {
            $row = [];
            foreach ($headers as $key => $header) {
                $row[] = $item[$key] ?? '';
            }
            $rowData[] = $row;
        }
        
        $sheet->fromArray($rowData, null, 'A2');
        
        // Auto-size columns
        foreach (range('A', $sheet->getHighestColumn()) as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }
        
        // Create a temporary file
        $filename = tempnam(sys_get_temp_dir(), 'report_') . '.xlsx';
        $writer = new Xlsx($spreadsheet);
        $writer->save($filename);
        
        // Return the Excel file as a download
        $output = file_get_contents($filename);
        unlink($filename);
        
        $filename = $reportType . '_report_' . date('Ymd_His') . '.xlsx';
        
        return $this->response
            ->withHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
            ->withHeader('Content-Disposition', 'attachment; filename="' . $filename . '"')
            ->withHeader('Content-Length', strlen($output))
            ->write($output);
    }
    
    private function generateCsv($data, $reportType) {
        $headers = $this->getExcelHeaders($reportType);
        
        // Create a temporary file
        $filename = tempnam(sys_get_temp_dir(), 'report_') . '.csv';
        $file = fopen($filename, 'w');
        
        // Add UTF-8 BOM for Excel compatibility
        fputs($file, chr(0xEF) . chr(0xBB) . chr(0xBF));
        
        // Write headers
        fputcsv($file, array_values($headers));
        
        // Write data rows
        foreach ($data['data'] as $item) {
            $row = [];
            foreach ($headers as $key => $header) {
                $row[] = $item[$key] ?? '';
            }
            fputcsv($file, $row);
        }
        
        fclose($file);
        
        // Return the CSV file as a download
        $output = file_get_contents($filename);
        unlink($filename);
        
        $filename = $reportType . '_report_' . date('Ymd_His') . '.csv';
        
        return $this->response
            ->withHeader('Content-Type', 'text/csv; charset=UTF-8')
            ->withHeader('Content-Disposition', 'attachment; filename="' . $filename . '"')
            ->withHeader('Content-Length', strlen($output))
            ->write($output);
    }
    
    private function getExcelHeaders($reportType) {
        $headers = [];
        
        switch ($reportType) {
            case 'loans':
                $headers = [
                    'loan_number' => 'Loan Number',
                    'client_name' => 'Client Name',
                    'product_name' => 'Loan Product',
                    'amount' => 'Loan Amount',
                    'term_months' => 'Term (Months)',
                    'interest_rate' => 'Interest Rate (%)',
                    'monthly_payment' => 'Monthly Payment',
                    'total_payable' => 'Total Payable',
                    'total_paid' => 'Total Paid',
                    'outstanding_balance' => 'Outstanding Balance',
                    'application_date' => 'Application Date',
                    'disbursement_date' => 'Disbursement Date',
                    'next_payment_date' => 'Next Payment Date',
                    'status' => 'Status'
                ];
                break;
                
            case 'payments':
                $headers = [
                    'payment_number' => 'Payment #',
                    'loan_number' => 'Loan #',
                    'client_name' => 'Client Name',
                    'product_name' => 'Loan Product',
                    'amount' => 'Amount',
                    'payment_date' => 'Payment Date',
                    'payment_method' => 'Payment Method',
                    'reference_number' => 'Reference #',
                    'notes' => 'Notes',
                    'received_by_name' => 'Received By'
                ];
                break;
                
            case 'clients':
                $headers = [
                    'client_id' => 'Client ID',
                    'first_name' => 'First Name',
                    'last_name' => 'Last Name',
                    'email' => 'Email',
                    'phone' => 'Phone',
                    'date_of_birth' => 'Date of Birth',
                    'gender' => 'Gender',
                    'occupation' => 'Occupation',
                    'monthly_income' => 'Monthly Income',
                    'address' => 'Address',
                    'city' => 'City',
                    'state' => 'State',
                    'postal_code' => 'Postal Code',
                    'country' => 'Country',
                    'total_loans' => 'Total Loans',
                    'total_borrowed' => 'Total Borrowed',
                    'total_repaid' => 'Total Repaid',
                    'status' => 'Status',
                    'created_at' => 'Registration Date'
                ];
                break;
                
            case 'collection':
                $headers = [
                    'collection_date' => 'Date',
                    'payment_method' => 'Payment Method',
                    'loan_count' => 'Loans',
                    'payment_count' => 'Payments',
                    'total_collected' => 'Amount Collected',
                    'collected_by' => 'Collected By'
                ];
                break;
                
            case 'portfolio':
                $headers = [
                    'product_name' => 'Loan Product',
                    'total_loans' => 'Total Loans',
                    'total_clients' => 'Total Clients',
                    'total_disbursed' => 'Total Disbursed',
                    'total_payable' => 'Total Payable',
                    'total_paid' => 'Total Paid',
                    'outstanding_balance' => 'Outstanding Balance',
                    'total_overdue' => 'Total Overdue',
                    'loans_30_days_overdue' => '30+ Days Overdue',
                    'loans_60_days_overdue' => '60+ Days Overdue',
                    'loans_90_days_overdue' => '90+ Days Overdue',
                    'par_30' => 'PAR 30+ (%)',
                    'par_60' => 'PAR 60+ (%)',
                    'par_90' => 'PAR 90+ (%)'
                ];
                break;
        }
        
        return $headers;
    }
    
    private function getPdfHtml($data, $reportType) {
        $html = '<!DOCTYPE html>
        <html>
        <head>
            <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
            <title>' . htmlspecialchars($data['title']) . '</title>
            <style>
                body { font-family: Arial, sans-serif; font-size: 10pt; }
                h1 { font-size: 16pt; color: #333; margin-bottom: 5px; }
                .header { margin-bottom: 20px; border-bottom: 1px solid #ddd; padding-bottom: 10px; }
                .filters { margin-bottom: 20px; font-size: 9pt; color: #666; }
                .filters span { font-weight: bold; }
                table { width: 100%; border-collapse: collapse; margin-top: 10px; }
                th { background-color: #f5f5f5; text-align: left; padding: 8px; border: 1px solid #ddd; font-size: 9pt; }
                td { padding: 6px; border: 1px solid #ddd; font-size: 9pt; }
                .summary { margin-top: 20px; padding: 10px; background-color: #f9f9f9; border: 1px solid #eee; }
                .summary h3 { margin-top: 0; }
                .text-right { text-align: right; }
                .text-center { text-align: center; }
                .page-break { page-break-after: always; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>' . htmlspecialchars($data['title']) . '</h1>
                <div>Generated on: ' . date('F j, Y, g:i a', strtotime($data['generated_at'])) . '</div>
            </div>';
        
        // Add filters if any
        if (!empty($data['filters'])) {
            $html .= '<div class="filters">';
            $html .= '<div><strong>Filters Applied:</strong></div>';
            foreach ($data['filters'] as $key => $value) {
                $html .= '<div>' . ucfirst(str_replace('_', ' ', $key)) . ': ' . htmlspecialchars($value) . '</div>';
            }
            $html .= '</div>';
        }
        
        // Add summary if available
        if (isset($data['summary'])) {
            $html .= '<div class="summary">';
            $html .= '<h3>Summary</h3>';
            
            foreach ($data['summary'] as $key => $value) {
                if (is_array($value)) {
                    $html .= '<div><strong>' . ucfirst(str_replace('_', ' ', $key)) . ':</strong> ' . 
                             htmlspecialchars(json_encode($value, JSON_PRETTY_PRINT)) . '</div>';
                } else {
                    $html .= '<div><strong>' . ucfirst(str_replace('_', ' ', $key)) . ':</strong> ' . 
                             (is_numeric($value) ? number_format($value, 2) : htmlspecialchars($value)) . '</div>';
                }
            }
            
            $html .= '</div>';
        }
        
        // Add data table
        if (!empty($data['data'])) {
            $headers = array_keys($data['data'][0]);
            
            $html .= '<table>
                <thead>
                    <tr>';
            
            foreach ($headers as $header) {
                $html .= '<th>' . ucfirst(str_replace('_', ' ', $header)) . '</th>';
            }
            
            $html .= '</tr>
                </thead>
                <tbody>';
            
            foreach ($data['data'] as $row) {
                $html .= '<tr>';
                
                foreach ($headers as $header) {
                    $value = $row[$header] ?? '';
                    
                    // Format values based on type
                    if (is_numeric($value) && strpos($header, 'amount') !== false || 
                        strpos($header, 'total') !== false || 
                        strpos($header, 'balance') !== false) {
                        $value = number_format($value, 2);
                    } elseif (strtotime($value) !== false && 
                             (strpos($header, 'date') !== false || 
                              strpos($header, 'created') !== false || 
                              strpos($header, 'updated') !== false)) {
                        $value = date('M j, Y', strtotime($value));
                    }
                    
                    $html .= '<td>' . htmlspecialchars($value) . '</td>';
                }
                
                $html .= '</tr>';
            }
            
            $html .= '</tbody>
            </table>';
        } else {
            $html .= '<p>No data available for the selected criteria.</p>';
        }
        
        $html .= '
        </body>
        </html>';
        
        return $html;
    }
}
