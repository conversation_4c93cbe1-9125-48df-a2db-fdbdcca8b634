<?php
require_once 'jwt.php';

class Auth {
    private $db;
    private $jwt;
    
    public function __construct() {
        $database = Database::getInstance();
        $this->db = $database->getConnection();
        $this->jwt = new JwtHandler();
    }
    
    public function login($username, $password) {
        $query = "SELECT id, username, password_hash, full_name, email, role FROM users WHERE username = :username AND status = 'active' LIMIT 1";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(":username", $username);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);

            if (password_verify($password, $row['password_hash'])) {
                unset($row['password_hash']);

                // Update last login
                $update_query = "UPDATE users SET last_login = NOW() WHERE id = :id";
                $update_stmt = $this->db->prepare($update_query);
                $update_stmt->bindParam(":id", $row['id']);
                $update_stmt->execute();

                $token = $this->jwt->generateJwtToken($row);

                return [
                    'status' => 'success',
                    'token' => $token,
                    'user' => $row
                ];
            }
        }
        
        return [
            'status' => 'error',
            'message' => 'Invalid username or password'
        ];
    }
    
    public function validateToken($token) {
        try {
            return $this->jwt->validateToken($token);
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }
    
    public function register($data) {
        // Validate input
        if (empty($data['username']) || empty($data['password']) || empty($data['email']) || empty($data['full_name'])) {
            return [
                'status' => 'error',
                'message' => 'All fields are required'
            ];
        }
        
        // Check if username or email already exists
        $query = "SELECT id FROM users WHERE username = :username OR email = :email LIMIT 1";
        $stmt = $this->db->prepare($query);
        $stmt->bindParam(":username", $data['username']);
        $stmt->bindParam(":email", $data['email']);
        $stmt->execute();
        
        if ($stmt->rowCount() > 0) {
            return [
                'status' => 'error',
                'message' => 'Username or email already exists'
            ];
        }
        
        // Hash password
        $hashed_password = password_hash($data['password'], PASSWORD_BCRYPT);
        
        // Insert new user
        $query = "INSERT INTO users (username, password_hash, full_name, email, role)
                 VALUES (:username, :password_hash, :full_name, :email, 'officer')";
        $stmt = $this->db->prepare($query);

        $stmt->bindParam(":username", $data['username']);
        $stmt->bindParam(":password_hash", $hashed_password);
        $stmt->bindParam(":full_name", $data['full_name']);
        $stmt->bindParam(":email", $data['email']);
        
        if ($stmt->execute()) {
            $user_id = $this->db->lastInsertId();
            
            // Get the newly created user
            $query = "SELECT id, username, full_name, email, role FROM users WHERE id = :id";
            $stmt = $this->db->prepare($query);
            $stmt->bindParam(":id", $user_id);
            $stmt->execute();
            
            $user = $stmt->fetch(PDO::FETCH_ASSOC);
            $token = $this->jwt->generateJwtToken($user);
            
            return [
                'status' => 'success',
                'token' => $token,
                'user' => $user
            ];
        }
        
        return [
            'status' => 'error',
            'message' => 'Registration failed. Please try again.'
        ];
    }
}
