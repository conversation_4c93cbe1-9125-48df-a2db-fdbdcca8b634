<?php
/**
 * Simple test script to verify system functionality
 */

echo "<h1>Microfinance System Test</h1>";

// Test 1: Database Connection
echo "<h2>1. Database Connection Test</h2>";
try {
    require_once 'config/database.php';
    $db = Database::getInstance()->getConnection();
    echo "<p style='color: green;'>✓ Database connection successful</p>";
    
    // Test if tables exist
    $tables = ['users', 'clients', 'loans', 'payments', 'loan_products'];
    foreach ($tables as $table) {
        try {
            $stmt = $db->query("SELECT COUNT(*) FROM $table");
            $count = $stmt->fetchColumn();
            echo "<p style='color: green;'>✓ Table '$table' exists with $count records</p>";
        } catch (Exception $e) {
            echo "<p style='color: red;'>✗ Table '$table' missing or error: " . $e->getMessage() . "</p>";
        }
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Database connection failed: " . $e->getMessage() . "</p>";
}

// Test 2: JWT Configuration
echo "<h2>2. JWT Configuration Test</h2>";
try {
    require_once 'config/jwt.php';
    $testPayload = ['user_id' => 1, 'username' => 'test'];
    $token = JWT::encode($testPayload);
    $decoded = JWT::decode($token);
    
    if ($decoded['user_id'] == 1 && $decoded['username'] == 'test') {
        echo "<p style='color: green;'>✓ JWT encoding/decoding working correctly</p>";
    } else {
        echo "<p style='color: red;'>✗ JWT payload mismatch</p>";
    }
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ JWT test failed: " . $e->getMessage() . "</p>";
}

// Test 3: Environment Variables
echo "<h2>3. Environment Variables Test</h2>";
$envVars = ['DB_HOST', 'DB_NAME', 'DB_USER', 'JWT_SECRET'];
foreach ($envVars as $var) {
    $value = getenv($var);
    if ($value !== false && !empty($value)) {
        echo "<p style='color: green;'>✓ $var is set</p>";
    } else {
        echo "<p style='color: red;'>✗ $var is not set or empty</p>";
    }
}

// Test 4: File Permissions
echo "<h2>4. File Permissions Test</h2>";
$directories = ['uploads', 'uploads/clients', 'uploads/documents'];
foreach ($directories as $dir) {
    if (is_dir($dir)) {
        if (is_writable($dir)) {
            echo "<p style='color: green;'>✓ Directory '$dir' exists and is writable</p>";
        } else {
            echo "<p style='color: orange;'>⚠ Directory '$dir' exists but is not writable</p>";
        }
    } else {
        echo "<p style='color: red;'>✗ Directory '$dir' does not exist</p>";
    }
}

// Test 5: API Endpoints
echo "<h2>5. API Endpoints Test</h2>";
$apiFiles = [
    'api/index.php' => 'Main API entry point',
    'api/router.php' => 'API router',
    'app/Controllers/AuthController.php' => 'Authentication controller',
    'app/Controllers/BaseController.php' => 'Base controller'
];

foreach ($apiFiles as $file => $description) {
    if (file_exists($file)) {
        echo "<p style='color: green;'>✓ $description exists</p>";
    } else {
        echo "<p style='color: red;'>✗ $description missing</p>";
    }
}

echo "<h2>Summary</h2>";
echo "<p>If all tests show green checkmarks (✓), your system is ready to use!</p>";
echo "<p>If you see any red X marks (✗), please fix those issues before proceeding.</p>";
echo "<p>Orange warnings (⚠) should be addressed for optimal functionality.</p>";

echo "<hr>";
echo "<p><a href='setup.php'>Go to Setup</a> | <a href='login.html'>Go to Login</a></p>";
?>
