<?php

class JWT {
    private static $secret_key;
    private static $algorithm = 'HS256';
    private static $expire_after = 86400; // 24 hours
    private static $leeway = 60; // 1 minute

    public static function init() {
        self::$secret_key = getenv('JWT_SECRET') ?: 'your-secret-key-here';
    }

    public static function encode($payload) {
        self::init();
        
        $header = json_encode([
            'typ' => 'JWT',
            'alg' => self::$algorithm
        ]);
        
        $payload['exp'] = time() + self::$expire_after;
        $payload['iat'] = time();
        
        $base64UrlHeader = self::base64UrlEncode($header);
        $base64UrlPayload = self::base64UrlEncode(json_encode($payload));
        
        $signature = hash_hmac('sha256', "$base64UrlHeader.$base64UrlPayload", self::$secret_key, true);
        $base64UrlSignature = self::base64UrlEncode($signature);
        
        return "$base64UrlHeader.$base64UrlPayload.$base64UrlSignature";
    }

    public static function decode($jwt) {
        self::init();
        
        if (empty($jwt)) {
            throw new Exception('Token not provided');
        }
        
        $tokenParts = explode('.', $jwt);
        
        if (count($tokenParts) !== 3) {
            throw new Exception('Invalid token format');
        }
        
        list($base64UrlHeader, $base64UrlPayload, $base64UrlSignature) = $tokenParts;
        
        $signature = self::base64UrlDecode($base64UrlSignature);
        $expectedSignature = hash_hmac('sha256', "$base64UrlHeader.$base64UrlPayload", self::$secret_key, true);
        
        if (!hash_equals($signature, $expectedSignature)) {
            throw new Exception('Invalid token signature');
        }
        
        $payload = json_decode(self::base64UrlDecode($base64UrlPayload), true);
        
        // Check if token has expired
        if (isset($payload['exp']) && $payload['exp'] < time() + self::$leeway) {
            throw new Exception('Token has expired');
        }
        
        return $payload;
    }
    
    public static function validateToken($token) {
        try {
            return self::decode($token);
        } catch (Exception $e) {
            return false;
        }
    }
    
    public static function getTokenFromHeader() {
        $headers = getallheaders();
        $authHeader = '';
        
        // Check for Authorization header
        if (isset($headers['Authorization'])) {
            $authHeader = $headers['Authorization'];
        } elseif (isset($headers['authorization'])) {
            $authHeader = $headers['authorization'];
        }
        
        // Extract the token
        if (preg_match('/Bearer\s(\S+)/', $authHeader, $matches)) {
            return $matches[1];
        }
        
        return null;
    }

    private static function base64UrlEncode($data) {
        return rtrim(strtr(base64_encode($data), '+/', '-_'), '=');
    }

    private static function base64UrlDecode($data) {
        return base64_decode(strtr($data, '-_', '+/'));
    }
}
