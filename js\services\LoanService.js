class LoanService {
    constructor() {
        this.apiService = apiService;
    }

    // Get all loans with optional filters
    async getLoans(filters = {}) {
        try {
            const data = await this.apiService.getLoans(filters);
            return { success: true, data };
        } catch (error) {
            console.error('Failed to fetch loans:', error);
            return { 
                success: false, 
                error: error.message || 'Failed to fetch loans. Please try again.'
            };
        }
    }

    // Get loan by ID
    async getLoan(loanId) {
        try {
            const data = await this.apiService.getLoan(loanId);
            return { success: true, data };
        } catch (error) {
            console.error(`Failed to fetch loan ${loanId}:`, error);
            return { 
                success: false, 
                error: error.message || 'Failed to fetch loan details. Please try again.'
            };
        }
    }

    // Create a new loan application
    async createLoan(loanData) {
        try {
            const data = await this.apiService.createLoan(loanData);
            return { success: true, data };
        } catch (error) {
            console.error('Failed to create loan:', error);
            return { 
                success: false, 
                error: error.message || 'Failed to create loan. Please check your input and try again.'
            };
        }
    }

    // Update loan status
    async updateLoanStatus(loanId, status) {
        try {
            const data = await this.apiService.updateLoanStatus(loanId, status);
            return { success: true, data };
        } catch (error) {
            console.error(`Failed to update loan status to ${status}:`, error);
            return { 
                success: false, 
                error: error.message || 'Failed to update loan status. Please try again.'
            };
        }
    }

    // Record a loan payment
    async recordPayment(paymentData) {
        try {
            const data = await this.apiService.recordPayment(paymentData);
            return { success: true, data };
        } catch (error) {
            console.error('Failed to record payment:', error);
            return { 
                success: false, 
                error: error.message || 'Failed to record payment. Please try again.'
            };
        }
    }

    // Get loan payments
    async getLoanPayments(loanId) {
        try {
            const data = await this.apiService.getLoanPayments(loanId);
            return { success: true, data };
        } catch (error) {
            console.error(`Failed to fetch payments for loan ${loanId}:`, error);
            return { 
                success: false, 
                error: error.message || 'Failed to fetch payment history. Please try again.'
            };
        }
    }

    // Calculate loan details (EMI, interest, etc.)
    calculateLoanDetails(principal, annualRate, termInMonths) {
        if (!principal || !annualRate || !termInMonths) {
            return {
                monthlyPayment: 0,
                totalPayment: 0,
                totalInterest: 0,
                amortizationSchedule: []
            };
        }

        const monthlyRate = annualRate / 100 / 12;
        const monthlyPayment = (principal * monthlyRate * Math.pow(1 + monthlyRate, termInMonths)) / 
                             (Math.pow(1 + monthlyRate, termInMonths) - 1);
        const totalPayment = monthlyPayment * termInMonths;
        const totalInterest = totalPayment - principal;

        // Generate amortization schedule
        let balance = principal;
        const schedule = [];
        
        for (let month = 1; month <= termInMonths; month++) {
            const interestPayment = balance * monthlyRate;
            const principalPayment = monthlyPayment - interestPayment;
            
            schedule.push({
                month,
                payment: monthlyPayment,
                principal: principalPayment,
                interest: interestPayment,
                balance: balance - principalPayment
            });
            
            balance -= principalPayment;
        }

        return {
            monthlyPayment: parseFloat(monthlyPayment.toFixed(2)),
            totalPayment: parseFloat(totalPayment.toFixed(2)),
            totalInterest: parseFloat(totalInterest.toFixed(2)),
            amortizationSchedule: schedule
        };
    }

    // Get loan status badge HTML
    getStatusBadge(status) {
        const statusMap = {
            'pending': { class: 'warning', label: 'Pending' },
            'approved': { class: 'info', label: 'Approved' },
            'disbursed': { class: 'primary', label: 'Disbursed' },
            'rejected': { class: 'danger', label: 'Rejected' },
            'closed': { class: 'success', label: 'Closed' },
            'written_off': { class: 'dark', label: 'Written Off' }
        };

        const statusInfo = statusMap[status] || { class: 'secondary', label: status };
        return `<span class="badge bg-${statusInfo.class}">${statusInfo.label}</span>`;
    }

    // Format loan data for display
    formatLoan(loan) {
        if (!loan) return null;
        
        return {
            ...loan,
            formattedAmount: formatCurrency(loan.amount),
            formattedInterestRate: `${loan.interestRate}%`,
            formattedDate: formatDate(loan.createdAt),
            statusBadge: this.getStatusBadge(loan.status),
            nextPaymentDate: loan.nextPaymentDate ? formatDate(loan.nextPaymentDate) : 'N/A',
            formattedMonthlyPayment: formatCurrency(loan.monthlyPayment)
        };
    }

    // Get loans summary for dashboard
    async getLoansSummary() {
        try {
            const [allLoans, activeLoans, pendingLoans] = await Promise.all([
                this.apiService.getLoans({ limit: 1 }),
                this.apiService.getLoans({ status: 'disbursed', limit: 1 }),
                this.apiService.getLoans({ status: 'pending', limit: 1 })
            ]);

            return {
                success: true,
                data: {
                    totalLoans: allLoans.total || 0,
                    activeLoans: activeLoans.total || 0,
                    pendingLoans: pendingLoans.total || 0,
                    totalDisbursed: activeLoans.data?.reduce((sum, loan) => sum + loan.amount, 0) || 0
                }
            };
        } catch (error) {
            console.error('Failed to fetch loans summary:', error);
            return { success: false, error: 'Failed to load loans summary' };
        }
    }
}

// Create a singleton instance
const loanService = new LoanService();

export default loanService;
