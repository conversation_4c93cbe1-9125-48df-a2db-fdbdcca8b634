// Global variables
let authToken = localStorage.getItem('authToken');
let currentUser = JSON.parse(localStorage.getItem('currentUser')) || {};
const API_BASE_URL = window.location.origin + '/sub%20system/api';

// DOM Elements
const sidebar = document.getElementById('sidebar');
const sidebarCollapse = document.getElementById('sidebarCollapse');
const content = document.getElementById('content');
const loginForm = document.getElementById('loginForm');
const dashboardSection = document.getElementById('dashboardSection');
const loginSection = document.getElementById('loginSection');
const logoutBtn = document.getElementById('logoutBtn');
const usernameDisplay = document.getElementById('usernameDisplay');

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    // Check if user is logged in
    checkAuth();
    
    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Event Listeners
    if (sidebarCollapse) {
        sidebarCollapse.addEventListener('click', toggleSidebar);
    }
    
    if (loginForm) {
        loginForm.addEventListener('submit', handleLogin);
    }
    
    if (logoutBtn) {
        logoutBtn.addEventListener('click', handleLogout);
    }
    
    // Load dashboard data if authenticated
    if (authToken) {
        loadDashboardData();
        setupNavigation();
    }
});

// Toggle sidebar
function toggleSidebar() {
    sidebar.classList.toggle('active');
    content.classList.toggle('active');
    
    // Store sidebar state in localStorage
    const isCollapsed = sidebar.classList.contains('active');
    localStorage.setItem('sidebarCollapsed', isCollapsed);
}

// Check authentication status
function checkAuth() {
    if (!authToken && !window.location.href.includes('login.html')) {
        window.location.href = 'login.html';
        return;
    }
    
    if (authToken && window.location.href.includes('login.html')) {
        window.location.href = 'index.html';
        return;
    }
    
    if (usernameDisplay && currentUser.username) {
        usernameDisplay.textContent = currentUser.username;
    }
}

// Handle login form submission
async function handleLogin(e) {
    e.preventDefault();
    
    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;
    
    try {
        const response = await fetch(`${API_BASE_URL}/auth/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ username, password })
        });
        
        const data = await response.json();
        
        if (!response.ok) {
            throw new Error(data.message || 'Login failed');
        }
        
        // Save token and user data
        authToken = data.token;
        currentUser = data.user;
        
        localStorage.setItem('authToken', authToken);
        localStorage.setItem('currentUser', JSON.stringify(currentUser));
        
        // Redirect to dashboard
        window.location.href = 'index.html';
        
    } catch (error) {
        showAlert('error', error.message || 'Login failed. Please try again.');
    }
}

// Handle logout
function handleLogout() {
    // Clear auth data
    localStorage.removeItem('authToken');
    localStorage.removeItem('currentUser');
    authToken = null;
    currentUser = {};
    
    // Redirect to login page
    window.location.href = 'login.html';
}

// Load dashboard data
async function loadDashboardData() {
    try {
        // Show loading state
        document.querySelectorAll('.card .card-body').forEach(cardBody => {
            cardBody.innerHTML = '<div class="text-center py-4"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>';
        });
        
        // Fetch dashboard stats
        const response = await fetch(`${API_BASE_URL}/dashboard/stats`, {
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            }
        });
        
        if (!response.ok) {
            throw new Error('Failed to load dashboard data');
        }
        
        const data = await response.json();
        
        // Update UI with dashboard data
        updateDashboardUI(data);
        
    } catch (error) {
        console.error('Error loading dashboard data:', error);
        showAlert('error', 'Failed to load dashboard data. ' + error.message);
    }
}

// Update dashboard UI with data
function updateDashboardUI(data) {
    // Update summary cards
    document.getElementById('totalLoans').textContent = formatCurrency(data.total_loans || 0);
    document.getElementById('activeLoans').textContent = data.active_loans || 0;
    document.getElementById('totalClients').textContent = data.total_clients || 0;
    document.getElementById('totalDisbursed').textContent = formatCurrency(data.total_disbursed || 0);
    document.getElementById('totalRepaid').textContent = formatCurrency(data.total_repaid || 0);
    document.getElementById('outstandingBalance').textContent = formatCurrency(data.outstanding_balance || 0);
    document.getElementById('collectionEfficiency').textContent = 
        (data.collection_efficiency?.efficiency_percentage || 0) + '%';
    
    // Update recent transactions table
    const tbody = document.querySelector('#recentTransactions tbody');
    if (tbody && data.recent_transactions) {
        tbody.innerHTML = data.recent_transactions.slice(0, 5).map(transaction => `
            <tr>
                <td>${transaction.payment_number || 'N/A'}</td>
                <td>${transaction.loan_number || 'N/A'}</td>
                <td>${transaction.client_name || 'N/A'}</td>
                <td>${formatDate(transaction.payment_date) || 'N/A'}</td>
                <td>${formatCurrency(transaction.amount) || '0.00'}</td>
                <td><span class="badge bg-success">${transaction.payment_method || 'N/A'}</span></td>
            </tr>
        `).join('');
    }
    
    // Update upcoming payments
    const upcomingPaymentsList = document.getElementById('upcomingPaymentsList');
    if (upcomingPaymentsList && data.upcoming_payments) {
        upcomingPaymentsList.innerHTML = data.upcoming_payments.slice(0, 5).map(payment => `
            <div class="list-group-item">
                <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">${payment.client_name || 'Client'}</h6>
                    <small>${formatDate(payment.next_payment_date)}</small>
                </div>
                <p class="mb-1">${formatCurrency(payment.due_amount)}</p>
                <small class="text-muted">${payment.days_until_due} days remaining</small>
            </div>
        `).join('');
    }
    
    // Update loan status chart (if Chart.js is included)
    if (window.Chart && data.loan_status_distribution) {
        updateLoanStatusChart(data.loan_status_distribution);
    }
}

// Setup navigation
function setupNavigation() {
    // Add click event listeners to all sidebar links
    document.querySelectorAll('.sidebar-nav a').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const target = this.getAttribute('href');
            
            // Remove active class from all links
            document.querySelectorAll('.sidebar-nav a').forEach(l => l.classList.remove('active'));
            
            // Add active class to clicked link
            this.classList.add('active');
            
            // Load the corresponding content
            loadContent(target);
        });
    });
}

// Load content dynamically
async function loadContent(page) {
    try {
        // Show loading state
        content.innerHTML = `
            <div class="d-flex justify-content-center align-items-center" style="height: 60vh;">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
            </div>`;
        
        // In a real app, you would fetch HTML content from the server
        // For this example, we'll just show a message
        let contentHtml = '';
        
        switch(page) {
            case 'dashboard':
                await loadDashboardData();
                return; // Dashboard is handled separately
                
            case 'clients':
                contentHtml = await loadClientsPage();
                break;
                
            case 'loans':
                contentHtml = await loadLoansPage();
                break;
                
            case 'reports':
                contentHtml = await loadReportsPage();
                break;
                
            case 'settings':
                contentHtml = '<h2>Settings</h2><p>Settings page content will go here.</p>';
                break;
                
            default:
                contentHtml = '<h2>Page Not Found</h2><p>The requested page could not be found.</p>';
        }
        
        content.innerHTML = contentHtml;
        
    } catch (error) {
        console.error('Error loading content:', error);
        showAlert('error', 'Failed to load content. ' + error.message);
    }
}

// Format currency
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 2
    }).format(amount);
}

// Format date
function formatDate(dateString) {
    if (!dateString) return 'N/A';
    
    const options = { 
        year: 'numeric', 
        month: 'short', 
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    };
    
    return new Date(dateString).toLocaleDateString('en-US', options);
}

// Show alert message
function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.role = 'alert';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;
    
    const alertContainer = document.getElementById('alertContainer');
    if (alertContainer) {
        alertContainer.prepend(alertDiv);
        
        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            const bsAlert = new bootstrap.Alert(alertDiv);
            bsAlert.close();
        }, 5000);
    }
}

// Initialize charts
function initCharts() {
    // Loan Status Chart
    const loanStatusCtx = document.getElementById('loanStatusChart');
    if (loanStatusCtx) {
        // This will be updated with real data when dashboard loads
        window.loanStatusChart = new Chart(loanStatusCtx, {
            type: 'doughnut',
            data: {
                labels: ['Active', 'Paid', 'Overdue', 'Defaulted'],
                datasets: [{
                    data: [0, 0, 0, 0],
                    backgroundColor: ['#4e73df', '#1cc88a', '#f6c23e', '#e74a3b'],
                    hoverBackgroundColor: ['#2e59d9', '#17a673', '#dda20a', '#be2617'],
                    hoverBorderColor: 'rgba(234, 236, 244, 1)',
                }]
            },
            options: {
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            usePointStyle: true,
                            padding: 20
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgb(255,255,255)',
                        bodyColor: '#858796',
                        borderColor: '#dddfeb',
                        borderWidth: 1,
                        displayColors: false,
                        caretPadding: 10,
                        callbacks: {
                            label: function(context) {
                                const label = context.label || '';
                                const value = context.raw || 0;
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = total > 0 ? Math.round((value / total) * 100) : 0;
                                return `${label}: ${value} (${percentage}%)`;
                            }
                        }
                    }
                },
                cutout: '70%',
            }
        });
    }
}

// Update loan status chart with data
function updateLoanStatusChart(statusData) {
    if (!window.loanStatusChart) return;
    
    const data = {
        active: statusData.active || 0,
        paid: statusData.paid || 0,
        overdue: statusData.overdue || 0,
        defaulted: statusData.defaulted || 0
    };
    
    window.loanStatusChart.data.datasets[0].data = Object.values(data);
    window.loanStatusChart.update();
}

// Initialize the application when the DOM is fully loaded
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initCharts);
} else {
    initCharts();
}
