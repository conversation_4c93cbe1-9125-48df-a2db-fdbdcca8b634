<?php

class DashboardController extends BaseController {
    public function getStats() {
        try {
            // Get summary statistics
            $stats = [
                'total_clients' => $this->getTotalClients(),
                'active_loans' => $this->getActiveLoansCount(),
                'total_loans' => $this->getTotalLoans(),
                'total_disbursed' => $this->getTotalDisbursed(),
                'total_repaid' => $this->getTotalRepaid(),
                'total_interest' => $this->getTotalInterest(),
                'outstanding_balance' => $this->getOutstandingBalance(),
                'loan_status_distribution' => $this->getLoanStatusDistribution(),
                'recent_transactions' => $this->getRecentTransactions(),
                'upcoming_payments' => $this->getUpcomingPayments(),
                'collection_efficiency' => $this->getCollectionEfficiency(),
                'loan_disbursement_trend' => $this->getLoanDisbursementTrend(),
                'portfolio_at_risk' => $this->getPortfolioAtRisk()
            ];
            
            return $this->success($stats);
            
        } catch (Exception $e) {
            return $this->error('Failed to fetch dashboard stats: ' . $e->getMessage());
        }
    }
    
    private function getTotalClients() {
        $stmt = $this->db->query("SELECT COUNT(*) as count FROM clients WHERE status = 'active'");
        return (int) $stmt->fetch()['count'];
    }
    
    private function getActiveLoansCount() {
        $stmt = $this->db->query("SELECT COUNT(*) as count FROM loans WHERE status = 'disbursed'");
        return (int) $stmt->fetch()['count'];
    }
    
    private function getTotalLoans() {
        $stmt = $this->db->query("SELECT COUNT(*) as count FROM loans");
        return (int) $stmt->fetch()['count'];
    }
    
    private function getTotalDisbursed() {
        $stmt = $this->db->query("SELECT COALESCE(SUM(amount), 0) as total FROM loans WHERE status = 'disbursed'");
        return (float) $stmt->fetch()['total'];
    }
    
    private function getTotalRepaid() {
        $stmt = $this->db->query("
            SELECT COALESCE(SUM(p.amount), 0) as total 
            FROM payments p
            JOIN loans l ON p.loan_id = l.id
            WHERE l.status = 'disbursed' OR l.status = 'closed'
        ");
        return (float) $stmt->fetch()['total'];
    }
    
    private function getTotalInterest() {
        $stmt = $this->db->query("
            SELECT COALESCE(SUM(total_payable - amount), 0) as total 
            FROM loans 
            WHERE status = 'disbursed' OR status = 'closed'
        ");
        return (float) $stmt->fetch()['total'];
    }
    
    private function getOutstandingBalance() {
        $stmt = $this->db->query("
            SELECT 
                COALESCE(SUM(l.total_payable - COALESCE(p.total_paid, 0)), 0) as balance
            FROM loans l
            LEFT JOIN (
                SELECT loan_id, SUM(amount) as total_paid 
                FROM payments 
                GROUP BY loan_id
            ) p ON l.id = p.loan_id
            WHERE l.status = 'disbursed'
        ");
        return (float) $stmt->fetch()['balance'];
    }
    
    private function getLoanStatusDistribution() {
        $stmt = $this->db->query("
            SELECT 
                status, 
                COUNT(*) as count,
                COALESCE(SUM(amount), 0) as total_amount
            FROM loans
            GROUP BY status
        ");
        
        $distribution = [
            'pending' => ['count' => 0, 'amount' => 0],
            'approved' => ['count' => 0, 'amount' => 0],
            'disbursed' => ['count' => 0, 'amount' => 0],
            'closed' => ['count' => 0, 'amount' => 0],
            'rejected' => ['count' => 0, 'amount' => 0],
            'written_off' => ['count' => 0, 'amount' => 0]
        ];
        
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $status = strtolower($row['status']);
            if (isset($distribution[$status])) {
                $distribution[$status] = [
                    'count' => (int) $row['count'],
                    'amount' => (float) $row['total_amount']
                ];
            }
        }
        
        return $distribution;
    }
    
    private function getRecentTransactions($limit = 10) {
        $stmt = $this->db->prepare("
            SELECT 
                p.id,
                p.payment_number,
                p.amount,
                p.payment_date,
                p.payment_method,
                p.reference_number,
                p.notes,
                p.created_at,
                l.loan_number,
                CONCAT(c.first_name, ' ', c.last_name) as client_name
            FROM payments p
            JOIN loans l ON p.loan_id = l.id
            JOIN clients c ON l.client_id = c.id
            ORDER BY p.payment_date DESC, p.created_at DESC
            LIMIT ?
        ");
        
        $stmt->execute([$limit]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    private function getUpcomingPayments($daysAhead = 7) {
        $stmt = $this->db->prepare("
            SELECT 
                l.id as loan_id,
                l.loan_number,
                c.id as client_id,
                CONCAT(c.first_name, ' ', c.last_name) as client_name,
                c.phone,
                l.next_payment_date,
                l.monthly_payment as due_amount,
                COALESCE(l.total_payable - COALESCE(p.total_paid, 0), l.total_payable) as outstanding_balance,
                DATEDIFF(l.next_payment_date, CURDATE()) as days_until_due
            FROM loans l
            JOIN clients c ON l.client_id = c.id
            LEFT JOIN (
                SELECT loan_id, SUM(amount) as total_paid 
                FROM payments 
                GROUP BY loan_id
            ) p ON l.id = p.loan_id
            WHERE l.status = 'disbursed'
            AND l.next_payment_date BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL ? DAY)
            ORDER BY l.next_payment_date ASC
        ");
        
        $stmt->execute([$daysAhead]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    private function getCollectionEfficiency() {
        // Get total amount due in the last 30 days
        $stmt = $this->db->query("
            SELECT 
                COALESCE(SUM(
                    CASE 
                        WHEN l.payment_frequency = 'weekly' THEN l.weekly_payment * 4
                        WHEN l.payment_frequency = 'biweekly' THEN l.monthly_payment * 0.5
                        ELSE l.monthly_payment
                    END
                ), 0) as total_due
            FROM loans l
            WHERE l.status = 'disbursed'
            AND l.next_payment_date BETWEEN DATE_SUB(CURDATE(), INTERVAL 30 DAY) AND CURDATE()
        ");
        
        $totalDue = (float) $stmt->fetch()['total_due'];
        
        // Get total amount collected in the last 30 days
        $stmt = $this->db->query("
            SELECT COALESCE(SUM(amount), 0) as total_collected
            FROM payments
            WHERE payment_date BETWEEN DATE_SUB(CURDATE(), INTERVAL 30 DAY) AND CURDATE()
        ");
        
        $totalCollected = (float) $stmt->fetch()['total_collected'];
        
        // Calculate collection efficiency (percentage)
        $efficiency = $totalDue > 0 ? ($totalCollected / $totalDue) * 100 : 100;
        
        return [
            'total_due' => $totalDue,
            'total_collected' => $totalCollected,
            'efficiency_percentage' => round(min(100, max(0, $efficiency)), 2)
        ];
    }
    
    private function getLoanDisbursementTrend($months = 6) {
        $stmt = $this->db->prepare("
            SELECT 
                DATE_FORMAT(disbursement_date, '%Y-%m') as month,
                COUNT(*) as loan_count,
                COALESCE(SUM(amount), 0) as total_disbursed
            FROM loans
            WHERE status = 'disbursed'
            AND disbursement_date >= DATE_SUB(CURDATE(), INTERVAL ? MONTH)
            GROUP BY DATE_FORMAT(disbursement_date, '%Y-%m')
            ORDER BY month
        ");
        
        $stmt->execute([$months]);
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Fill in missing months with zeros
        $trends = [];
        $currentDate = new DateTime();
        $currentDate->modify("first day of this month");
        
        for ($i = $months - 1; $i >= 0; $i--) {
            $month = $currentDate->format('Y-m');
            $found = false;
            
            foreach ($results as $row) {
                if ($row['month'] === $month) {
                    $trends[] = [
                        'month' => $month,
                        'loan_count' => (int) $row['loan_count'],
                        'total_disbursed' => (float) $row['total_disbursed']
                    ];
                    $found = true;
                    break;
                }
            }
            
            if (!$found) {
                $trends[] = [
                    'month' => $month,
                    'loan_count' => 0,
                    'total_disbursed' => 0.0
                ];
            }
            
            $currentDate->modify('-1 month');
        }
        
        return array_reverse($trends);
    }
    
    private function getPortfolioAtRisk($daysLate = 30) {
        // Get total outstanding portfolio
        $stmt = $this->db->query("
            SELECT 
                COALESCE(SUM(l.total_payable - COALESCE(p.total_paid, 0)), 0) as total_outstanding
            FROM loans l
            LEFT JOIN (
                SELECT loan_id, SUM(amount) as total_paid 
                FROM payments 
                GROUP BY loan_id
            ) p ON l.id = p.loan_id
            WHERE l.status = 'disbursed'
        ");
        
        $totalOutstanding = (float) $stmt->fetch()['total_outstanding'];
        
        if ($totalOutstanding <= 0) {
            return [
                'total_outstanding' => 0,
                'amount_at_risk' => 0,
                'percentage' => 0
            ];
        }
        
        // Get amount at risk (loans with payments overdue by $daysLate days or more)
        $stmt = $this->db->prepare("
            SELECT 
                COALESCE(SUM(l.total_payable - COALESCE(p.total_paid, 0)), 0) as amount_at_risk
            FROM loans l
            LEFT JOIN (
                SELECT loan_id, SUM(amount) as total_paid 
                FROM payments 
                GROUP BY loan_id
            ) p ON l.id = p.loan_id
            WHERE l.status = 'disbursed'
            AND l.next_payment_date < DATE_SUB(CURDATE(), INTERVAL ? DAY)
            AND (p.total_paid IS NULL OR p.total_paid < l.total_payable)
        ");
        
        $stmt->execute([$daysLate]);
        $amountAtRisk = (float) $stmt->fetch()['amount_at_risk'];
        
        $percentage = ($amountAtRisk / $totalOutstanding) * 100;
        
        return [
            'total_outstanding' => $totalOutstanding,
            'amount_at_risk' => $amountAtRisk,
            'percentage' => round($percentage, 2),
            'days_late_threshold' => $daysLate
        ];
    }
}
