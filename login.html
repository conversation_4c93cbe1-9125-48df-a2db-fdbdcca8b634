<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Microfinance Management System</title>
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-color: #667eea;
            --primary-dark: #5a6fd8;
            --secondary-color: #764ba2;
            --success-color: #10b981;
            --danger-color: #ef4444;
            --warning-color: #f59e0b;
            --info-color: #3b82f6;
            --light-color: #f8fafc;
            --dark-color: #1e293b;
            --border-radius: 12px;
            --box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            --transition: all 0.3s ease;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            position: relative;
            overflow-x: hidden;
        }

        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.1)"/><circle cx="10" cy="50" r="0.5" fill="rgba(255,255,255,0.1)"/><circle cx="90" cy="30" r="0.5" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            pointer-events: none;
        }

        .login-container {
            width: 100%;
            max-width: 420px;
            position: relative;
            z-index: 1;
        }

        .login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            border: 1px solid rgba(255, 255, 255, 0.2);
            overflow: hidden;
            transition: var(--transition);
        }

        .login-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        .card-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            text-align: center;
            padding: 2rem 1.5rem;
            border: none;
        }

        .logo-container {
            margin-bottom: 1rem;
        }

        .logo {
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto;
            font-size: 2rem;
            backdrop-filter: blur(10px);
        }

        .card-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }

        .card-subtitle {
            font-size: 0.9rem;
            opacity: 0.9;
            font-weight: 300;
        }

        .card-body {
            padding: 2rem;
        }

        .form-group {
            margin-bottom: 1.5rem;
            position: relative;
        }

        .form-control {
            border: 2px solid #e2e8f0;
            border-radius: var(--border-radius);
            padding: 0.75rem 1rem 0.75rem 3rem;
            font-size: 0.95rem;
            transition: var(--transition);
            background: #f8fafc;
        }

        .form-control:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            background: white;
        }

        .input-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: #64748b;
            z-index: 2;
        }

        .password-toggle {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #64748b;
            cursor: pointer;
            z-index: 2;
        }

        .btn-login {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
            border-radius: var(--border-radius);
            padding: 0.875rem 2rem;
            font-weight: 600;
            font-size: 0.95rem;
            letter-spacing: 0.025em;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }

        .btn-login:disabled {
            opacity: 0.7;
            transform: none;
        }

        .btn-login .spinner {
            display: none;
        }

        .btn-login.loading .spinner {
            display: inline-block;
        }

        .btn-login.loading .btn-text {
            display: none;
        }

        .remember-forgot {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
            font-size: 0.9rem;
        }

        .form-check-input:checked {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .forgot-link {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
            transition: var(--transition);
        }

        .forgot-link:hover {
            color: var(--primary-dark);
            text-decoration: underline;
        }

        .alert {
            border-radius: var(--border-radius);
            border: none;
            font-size: 0.9rem;
            margin-bottom: 1.5rem;
        }

        .alert-success {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
        }

        .alert-danger {
            background: rgba(239, 68, 68, 0.1);
            color: var(--danger-color);
        }

        .card-footer {
            background: #f8fafc;
            border-top: 1px solid #e2e8f0;
            padding: 1.5rem;
            text-align: center;
            font-size: 0.85rem;
            color: #64748b;
        }

        @media (max-width: 480px) {
            .login-container {
                max-width: 100%;
                margin: 0;
            }

            .card-body {
                padding: 1.5rem;
            }

            .card-header {
                padding: 1.5rem;
            }
        }

        .floating-shapes {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            pointer-events: none;
        }

        .shape {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        .shape:nth-child(1) {
            width: 80px;
            height: 80px;
            top: 10%;
            left: 10%;
            animation-delay: 0s;
        }

        .shape:nth-child(2) {
            width: 120px;
            height: 120px;
            top: 70%;
            right: 10%;
            animation-delay: 2s;
        }

        .shape:nth-child(3) {
            width: 60px;
            height: 60px;
            top: 40%;
            left: 80%;
            animation-delay: 4s;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
    </style>
</head>
<body>
    <!-- Floating Background Shapes -->
    <div class="floating-shapes">
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
    </div>

    <div class="login-container">
        <div class="login-card">
            <div class="card-header">
                <div class="logo-container">
                    <div class="logo">
                        <i class="fas fa-university"></i>
                    </div>
                </div>
                <h1 class="card-title">Welcome Back</h1>
                <p class="card-subtitle">Sign in to your Microfinance Management System</p>
            </div>

            <div class="card-body">
                <form id="loginForm">
                    <div id="alertContainer"></div>

                    <div class="form-group">
                        <i class="fas fa-user input-icon"></i>
                        <input type="text" class="form-control" id="username" placeholder="Enter your username" required autocomplete="username">
                    </div>

                    <div class="form-group">
                        <i class="fas fa-lock input-icon"></i>
                        <input type="password" class="form-control" id="password" placeholder="Enter your password" required autocomplete="current-password">
                        <button type="button" class="password-toggle" id="passwordToggle">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>

                    <div class="remember-forgot">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="rememberMe">
                            <label class="form-check-label" for="rememberMe">
                                Remember me
                            </label>
                        </div>
                        <a href="#" class="forgot-link">Forgot password?</a>
                    </div>

                    <button class="w-100 btn btn-primary btn-login" type="submit" id="loginButton">
                        <span class="spinner spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                        <span class="btn-text">
                            <i class="fas fa-sign-in-alt me-2"></i>Sign In
                        </span>
                    </button>
                </form>
            </div>

            <div class="card-footer">
                <small>© 2024 Microfinance Management System. All rights reserved.</small>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Enhanced Storage utility
        const storage = {
            set: (key, value) => {
                try {
                    if (typeof value === 'string') {
                        localStorage.setItem(key, value);
                    } else {
                        localStorage.setItem(key, JSON.stringify(value));
                    }
                    console.log(`Stored ${key}:`, value);
                } catch (e) {
                    console.error('Storage set error:', e);
                }
            },
            get: (key) => {
                try {
                    const item = localStorage.getItem(key);
                    if (!item) return null;

                    try {
                        return JSON.parse(item);
                    } catch {
                        return item;
                    }
                } catch (e) {
                    console.error('Storage get error:', e);
                    return null;
                }
            },
            remove: (key) => {
                try {
                    localStorage.removeItem(key);
                    console.log(`Removed ${key} from storage`);
                } catch (e) {
                    console.error('Storage remove error:', e);
                }
            }
        };

        // Enhanced API service
        class LoginApiService {
            constructor() {
                this.baseUrl = 'api';
            }

            async login(credentials) {
                try {
                    console.log('Making login request to:', `${this.baseUrl}/auth/login`);
                    console.log('Credentials:', { username: credentials.username, password: '***' });

                    const response = await fetch(`${this.baseUrl}/auth/login`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'application/json'
                        },
                        body: JSON.stringify(credentials)
                    });

                    console.log('Response status:', response.status);
                    console.log('Response headers:', [...response.headers.entries()]);

                    const responseText = await response.text();
                    console.log('Raw response:', responseText);

                    let data;
                    try {
                        data = JSON.parse(responseText);
                    } catch (parseError) {
                        console.error('JSON parse error:', parseError);
                        throw new Error('Invalid response from server');
                    }

                    console.log('Parsed response:', data);

                    if (!response.ok) {
                        throw new Error(data.message || 'Login failed');
                    }

                    // Store token and user data
                    if (data.data && data.data.token) {
                        storage.set('mfs_auth_token', data.data.token);
                        storage.set('mfs_user_data', data.data.user);
                        console.log('Token and user data stored successfully');
                    } else {
                        console.error('Response missing token or user data:', data);
                        throw new Error('Invalid login response format');
                    }

                    return data;
                } catch (error) {
                    console.error('Login error:', error);
                    throw error;
                }
            }
        }

        // Initialize API service
        const apiService = new LoginApiService();

        // Enhanced alert function
        function showAlert(message, type = 'danger') {
            const alertContainer = document.getElementById('alertContainer');
            const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
            const iconClass = type === 'success' ? 'fa-check-circle' : 'fa-exclamation-triangle';

            const alertHtml = `
                <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                    <i class="fas ${iconClass} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            alertContainer.innerHTML = alertHtml;

            // Auto-dismiss success alerts
            if (type === 'success') {
                setTimeout(() => {
                    const alert = alertContainer.querySelector('.alert');
                    if (alert) {
                        const bsAlert = new bootstrap.Alert(alert);
                        bsAlert.close();
                    }
                }, 3000);
            }
        }

        // Clear alerts
        function clearAlerts() {
            document.getElementById('alertContainer').innerHTML = '';
        }

        // Set loading state
        function setLoadingState(loading) {
            const button = document.getElementById('loginButton');
            const form = document.getElementById('loginForm');

            if (loading) {
                button.disabled = true;
                button.classList.add('loading');
                form.style.pointerEvents = 'none';
            } else {
                button.disabled = false;
                button.classList.remove('loading');
                form.style.pointerEvents = 'auto';
            }
        }

        // Enhanced form validation
        function validateForm(username, password) {
            const errors = [];

            if (!username || username.trim().length < 3) {
                errors.push('Username must be at least 3 characters long');
            }

            if (!password || password.length < 6) {
                errors.push('Password must be at least 6 characters long');
            }

            return errors;
        }

        // Main initialization
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Login page loaded');

            const loginForm = document.getElementById('loginForm');
            const passwordToggle = document.getElementById('passwordToggle');
            const passwordField = document.getElementById('password');
            const usernameField = document.getElementById('username');
            const rememberMe = document.getElementById('rememberMe');

            // Check if already logged in
            const token = storage.get('mfs_auth_token');
            if (token) {
                console.log('User already logged in, redirecting...');
                window.location.href = 'index.html';
                return;
            }

            // Password toggle functionality
            passwordToggle.addEventListener('click', function() {
                const type = passwordField.getAttribute('type') === 'password' ? 'text' : 'password';
                passwordField.setAttribute('type', type);

                const icon = this.querySelector('i');
                icon.classList.toggle('fa-eye');
                icon.classList.toggle('fa-eye-slash');
            });

            // Load saved username if available
            const savedUsername = localStorage.getItem('remembered_username');
            if (savedUsername) {
                usernameField.value = savedUsername;
                rememberMe.checked = true;
            }

            // Handle remember me functionality
            rememberMe.addEventListener('change', function() {
                if (this.checked) {
                    localStorage.setItem('remembered_username', usernameField.value);
                } else {
                    localStorage.removeItem('remembered_username');
                }
            });

            // Update saved username when typing
            usernameField.addEventListener('input', function() {
                if (rememberMe.checked) {
                    localStorage.setItem('remembered_username', this.value);
                }
            });

            // Handle form submission
            loginForm.addEventListener('submit', async function(e) {
                e.preventDefault();

                clearAlerts();

                const username = usernameField.value.trim();
                const password = passwordField.value;

                console.log('Form submitted with username:', username);

                // Validate form
                const validationErrors = validateForm(username, password);
                if (validationErrors.length > 0) {
                    showAlert(validationErrors.join('<br>'));
                    return;
                }

                setLoadingState(true);

                try {
                    const response = await apiService.login({
                        username: username,
                        password: password
                    });

                    console.log('Login successful:', response);
                    showAlert('Login successful! Redirecting to dashboard...', 'success');

                    // Verify storage
                    const storedToken = storage.get('mfs_auth_token');
                    const storedUser = storage.get('mfs_user_data');
                    console.log('Verification - Token stored:', !!storedToken);
                    console.log('Verification - User stored:', !!storedUser);

                    // Redirect after delay
                    setTimeout(() => {
                        console.log('Redirecting to dashboard...');
                        window.location.href = 'index.html';
                    }, 1500);

                } catch (error) {
                    console.error('Login failed:', error);
                    showAlert(error.message || 'Login failed. Please check your credentials and try again.');
                } finally {
                    setLoadingState(false);
                }
            });

            // Add some demo credentials for testing
            console.log('Demo credentials: admin / password');
        });
    </script>
</body>
</html>
