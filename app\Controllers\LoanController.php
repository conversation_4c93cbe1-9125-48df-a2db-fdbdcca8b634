<?php

class LoanController extends BaseController {
    public function index() {
        try {
            // Get query parameters
            $params = $this->request->getQueryParams();
            $page = max(1, (int) ($params['page'] ?? 1));
            $perPage = min(50, max(1, (int) ($params['per_page'] ?? 10)));
            $offset = ($page - 1) * $perPage;
            
            // Build base query
            $query = "SELECT l.*, c.first_name, c.last_name, c.phone, lp.name as product_name 
                     FROM loans l 
                     JOIN clients c ON l.client_id = c.id 
                     JOIN loan_products lp ON l.product_id = lp.id";
            
            $countQuery = "SELECT COUNT(*) as total FROM loans l";
            $where = [];
            $params = [];
            
            // Apply filters
            if (!empty($this->args['status'])) {
                $where[] = "l.status = ?";
                $params[] = $this->args['status'];
            }
            
            if (!empty($params['client_id'])) {
                $where[] = "l.client_id = ?";
                $params[] = $params['client_id'];
            }
            
            // Add WHERE clause if filters exist
            if (!empty($where)) {
                $query .= " WHERE " . implode(" AND ", $where);
                $countQuery .= " WHERE " . implode(" AND ", $where);
            }
            
            // Add ordering and pagination
            $query .= " ORDER BY l.created_at DESC LIMIT ? OFFSET ?";
            $params[] = $perPage;
            $params[] = $offset;
            
            // Get total count
            $stmt = $this->db->prepare($countQuery);
            $stmt->execute($params);
            $total = $stmt->fetch()['total'];
            
            // Get paginated results
            $stmt = $this->db->prepare($query);
            $stmt->execute($params);
            $loans = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Calculate pagination metadata
            $lastPage = ceil($total / $perPage);
            
            return $this->success([
                'data' => $loans,
                'meta' => [
                    'current_page' => $page,
                    'per_page' => $perPage,
                    'total' => (int) $total,
                    'last_page' => $lastPage,
                    'from' => $offset + 1,
                    'to' => min($offset + $perPage, $total)
                ]
            ]);
            
        } catch (Exception $e) {
            return $this->error('Failed to fetch loans: ' . $e->getMessage());
        }
    }
    
    public function show($id) {
        try {
            // Get loan details
            $stmt = $this->db->prepare("
                SELECT l.*, c.first_name, c.last_name, c.phone, c.email, 
                       lp.name as product_name, lp.interest_type, lp.payment_frequency
                FROM loans l
                JOIN clients c ON l.client_id = c.id
                JOIN loan_products lp ON l.product_id = lp.id
                WHERE l.id = ?
            ");
            $stmt->execute([$id]);
            $loan = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$loan) {
                return $this->error('Loan not found', 404);
            }
            
            // Get payment history
            $stmt = $this->db->prepare("
                SELECT * FROM payments 
                WHERE loan_id = ? 
                ORDER BY payment_date DESC, created_at DESC
            ");
            $stmt->execute([$id]);
            $payments = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // Calculate loan summary
            $summary = $this->calculateLoanSummary($loan, $payments);
            
            // Add additional data to loan
            $loan['payments'] = $payments;
            $loan['summary'] = $summary;
            
            return $this->success($loan);
            
        } catch (Exception $e) {
            return $this->error('Failed to fetch loan details: ' . $e->getMessage());
        }
    }
    
    public function store() {
        $data = $this->request->getParsedBody();
        
        // Validate input
        $validation = $this->validate([
            'client_id' => 'required|numeric',
            'product_id' => 'required|numeric',
            'amount' => 'required|numeric|min:1',
            'term_months' => 'required|numeric|min:1',
            'purpose' => 'required|min:10',
            'disbursement_date' => 'required|date'
        ]);
        
        if ($validation !== true) {
            return $validation;
        }
        
        try {
            // Start transaction
            $this->db->beginTransaction();
            
            // Get loan product details
            $stmt = $this->db->prepare("SELECT * FROM loan_products WHERE id = ? AND status = 'active'");
            $stmt->execute([$data['product_id']]);
            $product = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$product) {
                throw new Exception('Invalid loan product');
            }
            
            // Validate loan amount against product limits
            if ($data['amount'] < $product['min_amount'] || $data['amount'] > $product['max_amount']) {
                throw new Exception("Loan amount must be between {$product['min_amount']} and {$product['max_amount']}");
            }
            
            // Validate loan term against product limits
            if ($data['term_months'] < $product['term_min'] || $data['term_months'] > $product['term_max']) {
                throw new Exception("Loan term must be between {$product['term_min']} and {$product['term_max']} months");
            }
            
            // Calculate loan details
            $interestRate = $product['interest_rate'] / 100;
            $monthlyRate = $interestRate / 12;
            $termMonths = (int) $data['term_months'];
            $principal = (float) $data['amount'];
            
            // Calculate monthly payment (PMT formula)
            if ($product['interest_type'] === 'flat') {
                $totalInterest = $principal * $interestRate * ($termMonths / 12);
                $monthlyPayment = ($principal + $totalInterest) / $termMonths;
            } else {
                // Reducing balance
                if ($monthlyRate > 0) {
                    $monthlyPayment = ($principal * $monthlyRate * pow(1 + $monthlyRate, $termMonths)) / 
                                    (pow(1 + $monthlyRate, $termMonths) - 1);
                } else {
                    $monthlyPayment = $principal / $termMonths;
                }
            }
            
            $totalPayable = $monthlyPayment * $termMonths;
            
            // Generate loan number
            $loanNumber = 'LN-' . strtoupper(uniqid());
            
            // Create loan
            $stmt = $this->db->prepare("
                INSERT INTO loans (
                    loan_number, client_id, product_id, application_date, amount, 
                    term_months, interest_rate, monthly_payment, total_payable, 
                    purpose, status, created_by
                ) VALUES (?, ?, ?, CURDATE(), ?, ?, ?, ?, ?, ?, 'pending', ?)
            ");
            
            $stmt->execute([
                $loanNumber,
                $data['client_id'],
                $data['product_id'],
                $principal,
                $termMonths,
                $product['interest_rate'],
                round($monthlyPayment, 2),
                round($totalPayable, 2),
                $data['purpose'],
                $this->getCurrentUserId()
            ]);
            
            $loanId = $this->db->lastInsertId();
            
            // Commit transaction
            $this->db->commit();
            
            // Get the created loan
            return $this->show($loanId);
            
        } catch (Exception $e) {
            // Rollback transaction on error
            if ($this->db->inTransaction()) {
                $this->db->rollBack();
            }
            
            return $this->error('Failed to create loan: ' . $e->getMessage());
        }
    }
    
    public function updateStatus($id) {
        $data = $this->request->getParsedBody();
        
        // Validate input
        $validation = $this->validate([
            'status' => 'required|in:approved,rejected,disbursed,closed,written_off',
            'notes' => 'required_if:status,rejected,written_off',
            'disbursement_date' => 'required_if:status,disbursed|date',
            'disbursement_notes' => 'string|max:500'
        ]);
        
        if ($validation !== true) {
            return $validation;
        }
        
        try {
            // Start transaction
            $this->db->beginTransaction();
            
            // Get current loan status
            $stmt = $this->db->prepare("SELECT status FROM loans WHERE id = ?");
            $stmt->execute([$id]);
            $currentStatus = $stmt->fetchColumn();
            
            if (!$currentStatus) {
                throw new Exception('Loan not found');
            }
            
            // Validate status transition
            $newStatus = $data['status'];
            $validTransitions = $this->getValidStatusTransitions($currentStatus);
            
            if (!in_array($newStatus, $validTransitions)) {
                throw new Exception("Cannot change status from {$currentStatus} to {$newStatus}");
            }
            
            // Prepare update query based on status
            $updateFields = ['status = ?'];
            $updateParams = [$newStatus];
            
            $now = date('Y-m-d H:i:s');
            $currentUserId = $this->getCurrentUserId();
            
            switch ($newStatus) {
                case 'approved':
                    $updateFields[] = 'approved_date = ?';
                    $updateFields[] = 'approved_by = ?';
                    $updateParams[] = $now;
                    $updateParams[] = $currentUserId;
                    break;
                    
                case 'disbursed':
                    $updateFields[] = 'disbursement_date = ?';
                    $updateFields[] = 'disbursed_by = ?';
                    $updateFields[] = 'next_payment_date = ?';
                    
                    $disbursementDate = new DateTime($data['disbursement_date']);
                    $nextPaymentDate = $this->calculateNextPaymentDate($disbursementDate);
                    
                    $updateParams[] = $disbursementDate->format('Y-m-d');
                    $updateParams[] = $currentUserId;
                    $updateParams[] = $nextPaymentDate->format('Y-m-d');
                    break;
                    
                case 'closed':
                    $updateFields[] = 'closed_date = ?';
                    $updateFields[] = 'closed_by = ?';
                    $updateParams[] = $now;
                    $updateParams[] = $currentUserId;
                    break;
            }
            
            // Add updated_by and updated_at
            $updateFields[] = 'updated_at = ?';
            $updateParams[] = $now;
            
            // Update loan status
            $updateQuery = "UPDATE loans SET " . implode(', ', $updateFields) . " WHERE id = ?";
            $updateParams[] = $id;
            
            $stmt = $this->db->prepare($updateQuery);
            $stmt->execute($updateParams);
            
            // Add status change to audit log
            $this->logStatusChange($id, $currentStatus, $newStatus, $data['notes'] ?? null);
            
            // Commit transaction
            $this->db->commit();
            
            // Return updated loan
            return $this->show($id);
            
        } catch (Exception $e) {
            // Rollback transaction on error
            if ($this->db->inTransaction()) {
                $this->db->rollBack();
            }
            
            return $this->error('Failed to update loan status: ' . $e->getMessage());
        }
    }
    
    public function recordPayment($id) {
        $data = $this->request->getParsedBody();
        
        // Validate input
        $validation = $this->validate([
            'amount' => 'required|numeric|min:0.01',
            'payment_date' => 'required|date',
            'payment_method' => 'required|in:cash,bank_transfer,mobile_money,check,other',
            'reference_number' => 'string|max:100',
            'notes' => 'string|max:500'
        ]);
        
        if ($validation !== true) {
            return $validation;
        }
        
        try {
            // Start transaction
            $this->db->beginTransaction();
            
            // Get loan details
            $stmt = $this->db->prepare("
                SELECT l.*, lp.interest_type, lp.payment_frequency
                FROM loans l
                JOIN loan_products lp ON l.product_id = lp.id
                WHERE l.id = ? AND l.status = 'disbursed'
                FOR UPDATE
            ");
            $stmt->execute([$id]);
            $loan = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$loan) {
                throw new Exception('Loan not found or not eligible for payment');
            }
            
            // Generate payment number
            $paymentNumber = 'PYT-' . strtoupper(uniqid());
            
            // Record payment
            $stmt = $this->db->prepare("
                INSERT INTO payments (
                    payment_number, loan_id, amount, payment_date, 
                    payment_method, reference_number, notes, received_by
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $paymentNumber,
                $id,
                $data['amount'],
                $data['payment_date'],
                $data['payment_method'],
                $data['reference_number'] ?? null,
                $data['notes'] ?? null,
                $this->getCurrentUserId()
            ]);
            
            // Update loan status if fully paid
            $totalPaid = $this->getTotalPaid($id);
            $outstandingBalance = $loan['total_payable'] - $totalPaid;
            
            if ($outstandingBalance <= 0) {
                $stmt = $this->db->prepare("
                    UPDATE loans 
                    SET status = 'closed', 
                        closed_date = CURDATE(), 
                        closed_by = ?,
                        updated_at = NOW()
                    WHERE id = ?
                ");
                $stmt->execute([$this->getCurrentUserId(), $id]);
            }
            
            // Commit transaction
            $this->db->commit();
            
            // Return updated loan with payment details
            return $this->show($id);
            
        } catch (Exception $e) {
            // Rollback transaction on error
            if ($this->db->inTransaction()) {
                $this->db->rollBack();
            }
            
            return $this->error('Failed to record payment: ' . $e->getMessage());
        }
    }
    
    private function calculateLoanSummary($loan, $payments) {
        $totalPaid = array_sum(array_column($payments, 'amount'));
        $outstandingBalance = max(0, $loan['total_payable'] - $totalPaid);
        $nextPaymentDate = null;
        $nextPaymentAmount = null;
        $daysOverdue = 0;
        
        if ($loan['status'] === 'disbursed') {
            $today = new DateTime();
            $nextPayment = new DateTime($loan['next_payment_date']);
            
            if ($today > $nextPayment) {
                $daysOverdue = $today->diff($nextPayment)->days;
            }
            
            $nextPaymentDate = $loan['next_payment_date'];
            $nextPaymentAmount = $loan['monthly_payment'];
        }
        
        return [
            'total_loan_amount' => (float) $loan['amount'],
            'total_interest' => (float) ($loan['total_payable'] - $loan['amount']),
            'total_payable' => (float) $loan['total_payable'],
            'total_paid' => (float) $totalPaid,
            'outstanding_balance' => (float) $outstandingBalance,
            'next_payment_date' => $nextPaymentDate,
            'next_payment_amount' => $nextPaymentAmount,
            'days_overdue' => $daysOverdue,
            'payment_progress' => $loan['total_payable'] > 0 ? 
                min(100, round(($totalPaid / $loan['total_payable']) * 100, 2)) : 0
        ];
    }
    
    private function getValidStatusTransitions($currentStatus) {
        $transitions = [
            'pending' => ['approved', 'rejected'],
            'approved' => ['disbursed', 'rejected'],
            'disbursed' => ['closed', 'written_off'],
            'rejected' => [],
            'closed' => [],
            'written_off' => []
        ];
        
        return $transitions[$currentStatus] ?? [];
    }
    
    private function calculateNextPaymentDate(DateTime $disbursementDate) {
        // For simplicity, we'll set the first payment for the next month
        // In a real app, this would consider the payment frequency (weekly, biweekly, monthly)
        $nextPayment = clone $disbursementDate;
        return $nextPayment->modify('+1 month');
    }
    
    private function logStatusChange($loanId, $oldStatus, $newStatus, $notes = null) {
        $stmt = $this->db->prepare("
            INSERT INTO loan_status_history (
                loan_id, old_status, new_status, changed_by, notes, created_at
            ) VALUES (?, ?, ?, ?, ?, NOW())
        ");
        
        $stmt->execute([
            $loanId,
            $oldStatus,
            $newStatus,
            $this->getCurrentUserId(),
            $notes
        ]);
    }
    
    private function getTotalPaid($loanId) {
        $stmt = $this->db->prepare("SELECT COALESCE(SUM(amount), 0) as total FROM payments WHERE loan_id = ?");
        $stmt->execute([$loanId]);
        return (float) $stmt->fetchColumn();
    }
    
    private function getCurrentUserId() {
        // This would typically come from the JWT token in the request
        $token = JWT::getTokenFromHeader();
        if ($token) {
            $decoded = JWT::decode($token);
            return $decoded['user_id'] ?? null;
        }
        return null;
    }
}
