class DashboardService {
    constructor() {
        this.apiService = apiService;
    }

    // Get all dashboard statistics
    async getDashboardStats() {
        try {
            const response = await this.apiService.getDashboardStats();
            return { 
                success: true, 
                data: this.formatDashboardData(response.data) 
            };
        } catch (error) {
            console.error('Failed to fetch dashboard stats:', error);
            return { 
                success: false, 
                error: error.message || 'Failed to load dashboard statistics. Please try again.'
            };
        }
    }

    // Format dashboard data for display
    formatDashboardData(data) {
        if (!data) return null;

        // Format loan distribution for chart
        const loanDistribution = {
            labels: data.loan_distribution.map(item => item.product_name),
            datasets: [{
                data: data.loan_distribution.map(item => item.total_amount),
                backgroundColor: this.generateChartColors(data.loan_distribution.length)
            }]
        };

        // Format collection efficiency data for chart
        const collectionEfficiency = {
            labels: data.collection_efficiency.payments.map(item => 
                formatDate(item.payment_date, 'MMM YYYY')
            ),
            datasets: [{
                label: 'Amount Collected',
                data: data.collection_efficiency.payments.map(item => item.amount_collected),
                borderColor: '#4e73df',
                backgroundColor: 'rgba(78, 115, 223, 0.05)',
                fill: true
            }]
        };

        // Format recent transactions
        const recentTransactions = data.recent_transactions.map(tx => ({
            ...tx,
            formatted_date: formatDate(tx.transaction_date, 'DD/MM/YYYY HH:mm'),
            formatted_amount: formatCurrency(tx.amount),
            type_badge: this.getTransactionTypeBadge(tx.type)
        }));

        // Format upcoming payments
        const upcomingPayments = data.upcoming_payments.map(payment => ({
            ...payment,
            formatted_due_date: formatDate(payment.next_payment_date, 'DD/MM/YYYY'),
            formatted_amount: formatCurrency(payment.monthly_payment),
            formatted_outstanding: formatCurrency(payment.outstanding_balance),
            days_remaining: payment.days_remaining,
            urgency: this.getPaymentUrgency(payment.days_remaining)
        }));

        return {
            // Summary cards
            summary: {
                total_loans: data.total_loans.total_loans,
                active_loans: data.total_loans.active_loans,
                total_disbursed: formatCurrency(data.total_loans.total_disbursed),
                total_repaid: formatCurrency(data.total_loans.total_repaid),
                repayment_rate: data.total_loans.total_disbursed > 0 ? 
                    Math.round((data.total_loans.total_repaid / data.total_loans.total_disbursed) * 100) : 0,
                
                total_clients: data.total_clients.total_clients,
                active_clients: data.total_clients.active_clients,
                inactive_clients: data.total_clients.inactive_clients,
                blacklisted_clients: data.total_clients.blacklisted_clients,
                
                total_savings: formatCurrency(data.total_savings.total_balance),
                total_accounts: data.total_savings.total_accounts,
                regular_savings: formatCurrency(data.total_savings.regular_savings),
                fixed_deposits: formatCurrency(data.total_savings.fixed_deposits),
                
                collection_rate: data.collection_efficiency.collection_rate.toFixed(1)
            },
            
            // Charts data
            charts: {
                loan_distribution: loanDistribution,
                collection_efficiency: collectionEfficiency
            },
            
            // Tables data
            tables: {
                recent_transactions: recentTransactions,
                upcoming_payments: upcomingPayments
            }
        };
    }

    // Generate random colors for charts
    generateChartColors(count) {
        const colors = [
            '#4e73df', '#1cc88a', '#36b9cc', '#f6c23e', '#e74a3b',
            '#5a5c69', '#858796', '#3a3b45', '#1cc88a', '#36b9cc'
        ];
        return colors.slice(0, count);
    }

    // Get transaction type badge
    getTransactionTypeBadge(type) {
        const typeMap = {
            'payment': { class: 'success', label: 'Payment' },
            'disbursement': { class: 'primary', label: 'Disbursement' },
            'refund': { class: 'info', label: 'Refund' },
            'fee': { class: 'warning', label: 'Fee' },
            'adjustment': { class: 'secondary', label: 'Adjustment' }
        };

        const typeInfo = typeMap[type.toLowerCase()] || { class: 'secondary', label: type };
        return `<span class="badge bg-${typeInfo.class}">${typeInfo.label}</span>`;
    }

    // Get payment urgency level
    getPaymentUrgency(daysRemaining) {
        if (daysRemaining < 0) return 'overdue';
        if (daysRemaining === 0) return 'due-today';
        if (daysRemaining <= 3) return 'due-soon';
        return 'upcoming';
    }

    // Get performance metrics
    async getPerformanceMetrics(timeRange = '30d') {
        try {
            // In a real app, this would fetch from the API
            // const response = await this.apiService.getPerformanceMetrics(timeRange);
            // return response;
            
            // Mock data for now
            return {
                success: true,
                data: this.generateMockPerformanceData(timeRange)
            };
        } catch (error) {
            console.error('Failed to fetch performance metrics:', error);
            return { success: false, error: 'Failed to load performance metrics' };
        }
    }

    // Generate mock performance data (for demo purposes)
    generateMockPerformanceData(timeRange) {
        const now = new Date();
        let days = 30; // Default to 30 days
        
        if (timeRange === '7d') days = 7;
        else if (timeRange === '90d') days = 90;
        else if (timeRange === '1y') days = 365;

        const labels = [];
        const disbursements = [];
        const repayments = [];
        const newClients = [];

        for (let i = days - 1; i >= 0; i--) {
            const date = new Date(now);
            date.setDate(now.getDate() - i);
            
            labels.push(formatDate(date, 'MMM D'));
            disbursements.push(Math.floor(Math.random() * 5000) + 1000);
            repayments.push(Math.floor(Math.random() * 4000) + 500);
            newClients.push(Math.floor(Math.random() * 5) + 1);
        }

        return {
            labels,
            datasets: [
                {
                    label: 'Loan Disbursements',
                    data: disbursements,
                    borderColor: '#4e73df',
                    backgroundColor: 'rgba(78, 115, 223, 0.1)'
                },
                {
                    label: 'Loan Repayments',
                    data: repayments,
                    borderColor: '#1cc88a',
                    backgroundColor: 'rgba(28, 200, 138, 0.1)'
                },
                {
                    label: 'New Clients',
                    data: newClients,
                    borderColor: '#f6c23e',
                    backgroundColor: 'rgba(246, 194, 62, 0.1)',
                    yAxisID: 'y1'
                }
            ]
        };
    }
}

// Create a singleton instance
const dashboardService = new DashboardService();

export default dashboardService;
