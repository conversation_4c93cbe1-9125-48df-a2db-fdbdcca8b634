# Microfinance Management System

A comprehensive web-based microfinance management system built with PHP, MySQL, and Bootstrap. This system helps microfinance institutions manage clients, loans, payments, and generate reports.

## Features

- **User Management**: Admin, Officer, and Teller roles with different permissions
- **Client Management**: Complete client profiles with personal and financial information
- **Loan Management**: Loan applications, approvals, disbursements, and tracking
- **Payment Processing**: Record and track loan payments with multiple payment methods
- **Savings Accounts**: Manage client savings accounts and transactions
- **Reports**: Generate various financial and operational reports
- **Dashboard**: Real-time overview of key metrics and recent activities
- **Security**: JWT-based authentication and role-based access control

## Technology Stack

- **Backend**: PHP 7.4+
- **Database**: MySQL 5.7+
- **Frontend**: HTML5, CSS3, JavaScript (ES6+)
- **Framework**: Bootstrap 5.3
- **Icons**: Font Awesome 6.0
- **Authentication**: JWT (JSON Web Tokens)

## Installation

### Prerequisites

- XAMPP, WAMP, or similar PHP development environment
- PHP 7.4 or higher
- MySQL 5.7 or higher
- Web browser (Chrome, Firefox, Safari, Edge)

### Setup Instructions

1. **Download and Extract**
   - Extract the project files to your web server directory (e.g., `C:\xampp\htdocs\microfinance`)

2. **Database Setup**
   - Open phpMyAdmin or your preferred MySQL client
   - Import the database schema: `config/schema.sql`
   - This will create the `microfinance_db` database with all required tables

3. **Configuration**
   - The `.env` file is already configured for local development
   - Modify database credentials if needed:
     ```
     DB_HOST=localhost
     DB_NAME=microfinance_db
     DB_USER=root
     DB_PASS=
     ```

4. **Initial Setup**
   - Navigate to `http://localhost/microfinance/test.php` to verify system requirements
   - Go to `http://localhost/microfinance/setup.php` to create your admin user
   - Optionally load sample data for testing

5. **Access the System**
   - Visit `http://localhost/microfinance/login.html`
   - Login with your admin credentials

## Default Credentials (if using sample data)

- **Username**: admin
- **Password**: admin123

## Project Structure

```
microfinance/
├── api/                    # API endpoints
│   ├── controllers/        # API controllers
│   ├── index.php          # Main API entry point
│   ├── router.php         # API routing
│   ├── auth.php           # Authentication helper
│   └── jwt.php            # JWT handler
├── app/                   # Application logic
│   ├── Controllers/       # Main controllers
│   └── Middleware/        # Middleware classes
├── config/                # Configuration files
│   ├── database.php       # Database configuration
│   ├── jwt.php           # JWT configuration
│   ├── schema.sql        # Database schema
│   └── sample_data.sql   # Sample data
├── css/                   # Stylesheets
├── js/                    # JavaScript files
│   ├── services/         # API service classes
│   ├── config.js         # App configuration
│   ├── login.js          # Login functionality
│   └── dashboard.js      # Dashboard functionality
├── pages/                 # Additional pages
├── uploads/              # File uploads directory
├── dashboard.html        # Dashboard page
├── login.html            # Login page
├── setup.php             # Setup script
├── test.php              # System test script
└── README.md             # This file
```

## Usage

### User Roles

1. **Admin**: Full system access, user management, system configuration
2. **Officer**: Client and loan management, payment processing
3. **Teller**: Payment processing, basic client operations

### Key Workflows

1. **Client Onboarding**
   - Add new client with personal details
   - Upload required documents
   - Create savings account (optional)

2. **Loan Processing**
   - Create loan application
   - Review and approve/reject
   - Disburse approved loans
   - Track repayments

3. **Payment Management**
   - Record loan payments
   - Process savings deposits/withdrawals
   - Generate payment receipts

## API Endpoints

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/register` - User registration
- `GET /api/auth/me` - Get current user info

### Clients
- `GET /api/clients` - List all clients
- `POST /api/clients` - Create new client
- `GET /api/clients/{id}` - Get client details
- `PUT /api/clients/{id}` - Update client
- `DELETE /api/clients/{id}` - Delete client

### Loans
- `GET /api/loans` - List all loans
- `POST /api/loans` - Create new loan
- `GET /api/loans/{id}` - Get loan details
- `PUT /api/loans/{id}` - Update loan
- `POST /api/loans/{id}/payments` - Record payment

## Security Features

- Password hashing using bcrypt
- JWT-based authentication
- Role-based access control
- SQL injection prevention
- XSS protection
- File upload restrictions

## Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Check MySQL service is running
   - Verify database credentials in `.env`
   - Ensure database exists

2. **Login Issues**
   - Clear browser cache and cookies
   - Check if admin user exists
   - Verify JWT secret is set

3. **File Upload Issues**
   - Check uploads directory permissions
   - Verify PHP upload settings
   - Check file size limits

### Getting Help

1. Run `test.php` to diagnose system issues
2. Check browser console for JavaScript errors
3. Review PHP error logs
4. Ensure all required files are present

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## License

This project is open source and available under the MIT License.

## Support

For support and questions, please create an issue in the project repository or contact the development team.
