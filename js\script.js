// Loan Calculator Function
function calculateLoan(amount, interestRate, term) {
    const monthlyInterestRate = (interestRate / 100) / 12;
    const monthlyPayment = (amount * monthlyInterestRate) / (1 - Math.pow(1 + monthlyInterestRate, -term));
    const totalPayment = monthlyPayment * term;
    const totalInterest = totalPayment - amount;
    
    return {
        monthlyPayment: monthlyPayment.toFixed(2),
        totalInterest: totalInterest.toFixed(2),
        totalPayment: totalPayment.toFixed(2)
    };
}

document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    const tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Toggle sidebar
        document.getElementById('sidebar').classList.toggle('active');
        document.getElementById('content').classList.toggle('active');
    });

    // Initialize charts
    initCharts();
    
    // Add active class to current nav item
    const currentLocation = location.href;
    const menuItems = document.querySelectorAll('#sidebar a');
    const menuLength = menuItems.length;
    
    for (let i = 0; i < menuLength; i++) {
        if (menuItems[i].href === currentLocation) {
            menuItems[i].parentElement.classList.add('active');
            
            // Expand parent dropdown if in a submenu
            const parent = menuItems[i].closest('.collapse');
            if (parent) {
                parent.classList.add('show');
                parent.parentElement.classList.add('active');
            }
        }
    }
});

function initCharts() {
    // Loan Portfolio Chart
    const loanCtx = document.getElementById('loanChart').getContext('2d');
    new Chart(loanCtx, {
        type: 'bar',
        data: {
            labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul'],
            datasets: [{
                label: 'Disbursed Loans',
                data: [120000, 150000, 180000, 140000, 200000, 180000, 220000],
                backgroundColor: 'rgba(54, 162, 235, 0.6)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 1
            }, {
                label: 'Repaid Loans',
                data: [100000, 130000, 150000, 120000, 180000, 160000, 200000],
                backgroundColor: 'rgba(75, 192, 192, 0.6)',
                borderColor: 'rgba(75, 192, 192, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '$' + value.toLocaleString();
                        }
                    }
                }
            },
            plugins: {
                legend: {
                    position: 'top',
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return context.dataset.label + ': $' + context.raw.toLocaleString();
                        }
                    }
                }
            }
        }
    });

    // Gauge Chart
    const gaugeCtx = document.getElementById('gaugeChart').getContext('2d');
    new Chart(gaugeCtx, {
        type: 'doughnut',
        data: {
            datasets: [{
                data: [98.5, 1.5],
                backgroundColor: ['#4e73df', '#e3e6f0'],
                borderWidth: 0,
                circumference: 180,
                rotation: 270
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            cutout: '70%',
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    enabled: false
                }
            }
        }
    });
}

// Add animation to cards on scroll
const animateOnScroll = function() {
    const cards = document.querySelectorAll('.card');
    
    cards.forEach(card => {
        const cardPosition = card.getBoundingClientRect().top;
        const screenPosition = window.innerHeight / 1.3;
        
        if (cardPosition < screenPosition) {
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }
    });
};

// Initialize cards with opacity 0 for animation
document.addEventListener('DOMContentLoaded', function() {
    const cards = document.querySelectorAll('.card');
    cards.forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        card.style.transition = 'opacity 0.5s ease-out, transform 0.5s ease-out';
    });
    
    // Initial check in case cards are already in view
    animateOnScroll();
});

// Add scroll event listener
window.addEventListener('scroll', animateOnScroll);

    // Loan Calculator Form Submission
    const loanCalculatorForm = document.getElementById('loanCalculatorForm');
    if (loanCalculatorForm) {
        document.getElementById('calculateBtn').addEventListener('click', function() {
            const amount = parseFloat(document.getElementById('loanAmount').value);
            const rate = parseFloat(document.getElementById('interestRate').value);
            const term = parseInt(document.getElementById('loanTerm').value);
            
            if (isNaN(amount) || isNaN(rate) || isNaN(term) || amount <= 0 || rate < 0 || term <= 0) {
                Swal.fire({
                    icon: 'error',
                    title: 'Invalid Input',
                    text: 'Please enter valid values for all fields.',
                    confirmButtonColor: '#3498db'
                });
                return;
            }
            
            const result = calculateLoan(amount, rate, term);
            document.getElementById('monthlyPayment').textContent = '$' + result.monthlyPayment;
            document.getElementById('totalInterest').textContent = '$' + result.totalInterest;
        });
    }

    // Quick Action Buttons
    const quickActionsBtn = document.getElementById('quickActionsBtn');
    const quickActionsMenu = document.querySelector('.quick-actions-menu');
    
    if (quickActionsBtn) {
        quickActionsBtn.addEventListener('click', function(e) {
            e.stopPropagation();
            quickActionsMenu.style.display = quickActionsMenu.style.display === 'flex' ? 'none' : 'flex';
        });
    }
    
    // Close quick actions when clicking outside
    document.addEventListener('click', function() {
        if (quickActionsMenu) {
            quickActionsMenu.style.display = 'none';
        }
    });

    // New Client Button
    const newClientBtn = document.getElementById('newClientBtn');
    if (newClientBtn) {
        newClientBtn.addEventListener('click', function() {
            Swal.fire({
                title: 'Add New Client',
                html: `
                    <div class="mb-3">
                        <input type="text" id="clientName" class="form-control" placeholder="Full Name" required>
                    </div>
                    <div class="mb-3">
                        <input type="email" id="clientEmail" class="form-control" placeholder="Email" required>
                    </div>
                    <div class="mb-3">
                        <input type="tel" id="clientPhone" class="form-control" placeholder="Phone Number">
                    </div>
                `,
                showCancelButton: true,
                confirmButtonText: 'Save Client',
                cancelButtonText: 'Cancel',
                confirmButtonColor: '#3498db',
                showLoaderOnConfirm: true,
                preConfirm: () => {
                    const name = document.getElementById('clientName').value;
                    const email = document.getElementById('clientEmail').value;
                    const phone = document.getElementById('clientPhone').value;
                    
                    if (!name || !email) {
                        Swal.showValidationMessage('Name and Email are required');
                        return false;
                    }
                    
                    // Here you would typically send this data to your server
                    return { name, email, phone };
                },
                allowOutsideClick: () => !Swal.isLoading()
            }).then((result) => {
                if (result.isConfirmed) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Client Added!',
                        text: `${result.value.name} has been added to the system.`,
                        confirmButtonColor: '#3498db'
                    });
                }
            });
        });
    }
    
    // New Payment Button
    const newPaymentBtn = document.getElementById('newPaymentBtn');
    if (newPaymentBtn) {
        newPaymentBtn.addEventListener('click', function() {
            Swal.fire({
                title: 'Record New Payment',
                html: `
                    <div class="mb-3">
                        <select class="form-select" id="paymentClient">
                            <option value="" selected disabled>Select Client</option>
                            <option value="1">John Doe</option>
                            <option value="2">Jane Smith</option>
                            <option value="3">Robert Johnson</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <input type="number" id="paymentAmount" class="form-control" placeholder="Amount" required>
                    </div>
                    <div class="mb-3">
                        <input type="date" id="paymentDate" class="form-control" required>
                    </div>
                    <div class="mb-3">
                        <select class="form-select" id="paymentMethod">
                            <option value="cash">Cash</option>
                            <option value="bank">Bank Transfer</option>
                            <option value="mobile">Mobile Money</option>
                            <option value="check">Check</option>
                        </select>
                    </div>
                `,
                showCancelButton: true,
                confirmButtonText: 'Record Payment',
                cancelButtonText: 'Cancel',
                confirmButtonColor: '#3498db',
                preConfirm: () => {
                    const client = document.getElementById('paymentClient').value;
                    const amount = document.getElementById('paymentAmount').value;
                    const date = document.getElementById('paymentDate').value;
                    
                    if (!client || !amount || !date) {
                        Swal.showValidationMessage('All fields are required');
                        return false;
                    }
                    
                    return { client, amount, date };
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Payment Recorded!',
                        text: `Payment of $${result.value.amount} has been recorded.`,
                        confirmButtonColor: '#3498db'
                    });
                }
            });
        });
    }
