<?php
require_once 'BaseController.php';

class DashboardController extends BaseController {
    
    public function __construct() {
        parent::__construct();
    }
    
    public function getStats() {
        if (!$this->user_data) {
            $this->sendResponse(401, ['status' => 'error', 'message' => 'Unauthorized']);
            return;
        }
        
        $stats = [
            'total_loans' => $this->getTotalLoans(),
            'total_clients' => $this->getTotalClients(),
            'total_savings' => $this->getTotalSavings(),
            'total_payments' => $this->getTotalPayments(),
            'loan_distribution' => $this->getLoanDistribution(),
            'collection_efficiency' => $this->getCollectionEfficiency(),
            'recent_transactions' => $this->getRecentTransactions(),
            'upcoming_payments' => $this->getUpcomingPayments()
        ];
        
        $this->sendResponse(200, [
            'status' => 'success',
            'data' => $stats
        ]);
    }
    
    private function getTotalLoans() {
        $query = "SELECT 
                    COUNT(*) as total_loans,
                    SUM(CASE WHEN status = 'disbursed' THEN 1 ELSE 0 END) as active_loans,
                    SUM(CASE WHEN status = 'disbursed' THEN amount ELSE 0 END) as total_disbursed,
                    SUM(CASE WHEN status = 'disbursed' THEN 
                        (SELECT COALESCE(SUM(amount), 0) FROM payments WHERE loan_id = loans.id)
                    ELSE 0 END) as total_repaid
                 FROM loans";
        
        $stmt = $this->db->prepare($query);
        $stmt->execute();
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    private function getTotalClients() {
        $query = "SELECT 
                    COUNT(*) as total_clients,
                    SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_clients,
                    SUM(CASE WHEN status = 'inactive' THEN 1 ELSE 0 END) as inactive_clients,
                    SUM(CASE WHEN status = 'blacklisted' THEN 1 ELSE 0 END) as blacklisted_clients
                 FROM clients";
        
        $stmt = $this->db->prepare($query);
        $stmt->execute();
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    private function getTotalSavings() {
        $query = "SELECT 
                    COUNT(*) as total_accounts,
                    SUM(balance) as total_balance,
                    SUM(CASE WHEN account_type = 'regular' THEN balance ELSE 0 END) as regular_savings,
                    SUM(CASE WHEN account_type = 'fixed_deposit' THEN balance ELSE 0 END) as fixed_deposits
                 FROM savings_accounts";
        
        $stmt = $this->db->prepare($query);
        $stmt->execute();
        
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }
    
    private function getTotalPayments() {
        $query = "SELECT 
                    COUNT(*) as total_payments,
                    SUM(amount) as total_amount,
                    DATE(payment_date) as payment_date
                 FROM payments 
                 WHERE payment_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
                 GROUP BY DATE(payment_date)
                 ORDER BY payment_date DESC
                 LIMIT 30";
        
        $stmt = $this->db->prepare($query);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    private function getLoanDistribution() {
        $query = "SELECT 
                    p.name as product_name,
                    COUNT(l.id) as loan_count,
                    SUM(l.amount) as total_amount
                 FROM loans l
                 JOIN loan_products p ON l.product_id = p.id
                 WHERE l.status = 'disbursed'
                 GROUP BY p.id, p.name
                 ORDER BY total_amount DESC";
        
        $stmt = $this->db->prepare($query);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    private function getCollectionEfficiency() {
        $query = "SELECT 
                    DATE(payment_date) as payment_date,
                    COUNT(*) as payment_count,
                    SUM(amount) as amount_collected
                 FROM payments
                 WHERE payment_date >= DATE_SUB(CURDATE(), INTERVAL 12 MONTH)
                 GROUP BY DATE(payment_date)
                 ORDER BY payment_date";
        
        $stmt = $this->db->prepare($query);
        $stmt->execute();
        
        $payments = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Calculate collection rate (simplified)
        $query = "SELECT 
                    COUNT(*) as total_due,
                    SUM(CASE WHEN p.id IS NOT NULL THEN 1 ELSE 0 END) as total_paid
                 FROM loans l
                 LEFT JOIN payments p ON l.id = p.loan_id
                 WHERE l.status = 'disbursed'
                 AND l.end_date >= DATE_SUB(CURDATE(), INTERVAL 12 MONTH)";
        
        $stmt = $this->db->prepare($query);
        $stmt->execute();
        $collection_rate = $stmt->fetch(PDO::FETCH_ASSOC);
        
        return [
            'payments' => $payments,
            'collection_rate' => $collection_rate['total_due'] > 0 ? 
                round(($collection_rate['total_paid'] / $collection_rate['total_due']) * 100, 2) : 0
        ];
    }
    
    private function getRecentTransactions($limit = 10) {
        $query = "SELECT 
                    t.transaction_number,
                    t.type,
                    t.amount,
                    t.transaction_date,
                    t.description,
                    c.client_id,
                    CONCAT(c.first_name, ' ', c.last_name) as client_name,
                    l.loan_number
                 FROM transactions t
                 LEFT JOIN loans l ON t.loan_id = l.id
                 LEFT JOIN clients c ON l.client_id = c.id
                 ORDER BY t.transaction_date DESC
                 LIMIT :limit";
        
        $stmt = $this->db->prepare($query);
        $stmt->bindValue(':limit', (int)$limit, PDO::PARAM_INT);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    private function getUpcomingPayments($limit = 10) {
        $query = "SELECT 
                    l.id as loan_id,
                    l.loan_number,
                    c.client_id,
                    CONCAT(c.first_name, ' ', c.last_name) as client_name,
                    l.amount as loan_amount,
                    l.monthly_payment,
                    l.next_payment_date,
                    DATEDIFF(l.next_payment_date, CURDATE()) as days_remaining,
                    (SELECT COALESCE(SUM(amount), 0) FROM payments p WHERE p.loan_id = l.id) as amount_paid,
                    l.amount - (SELECT COALESCE(SUM(amount), 0) FROM payments p WHERE p.loan_id = l.id) as outstanding_balance
                 FROM loans l
                 JOIN clients c ON l.client_id = c.id
                 WHERE l.status = 'disbursed'
                 AND l.next_payment_date IS NOT NULL
                 AND l.next_payment_date >= CURDATE()
                 ORDER BY l.next_payment_date ASC
                 LIMIT :limit";
        
        $stmt = $this->db->prepare($query);
        $stmt->bindValue(':limit', (int)$limit, PDO::PARAM_INT);
        $stmt->execute();
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
}
