<?php

class AuthMiddleware {
    public function __invoke($request, $response, $next) {
        // Skip authentication for these paths
        $publicPaths = ['/api/auth/login', '/api/auth/register'];
        
        if (in_array($request->getUri()->getPath(), $publicPaths)) {
            return $next($request, $response);
        }
        
        // Get token from header
        $token = JWT::getTokenFromHeader();
        
        if (!$token) {
            return $response->with<PERSON>son([
                'status' => 'error',
                'message' => 'No token provided'
            ], 401);
        }
        
        try {
            // Validate token
            $decoded = JWT::decode($token);
            
            // Add user data to request
            $request = $request->withAttribute('user', $decoded);
            
            return $next($request, $response);
            
        } catch (Exception $e) {
            return $response->with<PERSON>son([
                'status' => 'error',
                'message' => $e->getMessage()
            ], 401);
        }
    }
}
