# Enable URL rewriting
RewriteEngine On

# Set the base directory (adjust if your project is in a subdirectory)
RewriteBase /sub%20system/

# If the request is for a file or directory that exists, serve it directly
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d

# Route all API requests to the router.php file
RewriteRule ^api/(.*)$ api/router.php [QSA,L]

# For frontend routing (e.g., if using a single-page application)
# This will redirect all non-API requests to index.html
# Make sure to uncomment and adjust if you have a frontend SPA
# RewriteCond %{REQUEST_FILENAME} !-f
# RewriteCond %{REQUEST_FILENAME} !-d
# RewriteRule ^(.*)$ index.html [L]

# Set default character set
AddDefaultCharset UTF-8

# Disable directory listing
Options -Indexes

# Protect sensitive files
<FilesMatch "^\.|composer\.(json|lock)|package(-lock)?\.json|web\.config|yarn\.lock|Dockerfile|docker-compose\.ya?ml|README\.md|CHANGELOG\.md|LICENSE\.txt|\.gitignore$">
    Order allow,deny
    Deny from all
</FilesMatch>

# Protect .env file
<Files ".env">
    Order allow,deny
    Deny from all
</Files>

# Set PHP settings
<IfModule mod_php7.c>
    php_value upload_max_filesize 20M
    php_value post_max_size 21M
    php_value memory_limit 256M
    php_value max_execution_time 300
    php_value max_input_time 300
    php_value session.gc_maxlifetime 1440
</IfModule>

# Enable CORS for API
<IfModule mod_headers.c>
    Header set Access-Control-Allow-Origin "*"
    Header set Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
    Header set Access-Control-Allow-Headers "Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With, X-API-Key"
    
    # Handle preflight requests
    RewriteCond %{REQUEST_METHOD} OPTIONS
    RewriteRule ^(.*)$ $1 [R=200,L]
</IfModule>

# GZIP compression for better performance
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
    AddOutputFilterByType DEFLATE application/json
</IfModule>

# Enable browser caching for static assets
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 year"
    ExpiresByType image/jpeg "access plus 1 year"
    ExpiresByType image/gif "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType text/x-javascript "access plus 1 month"
    ExpiresByType application/x-shockwave-flash "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
    ExpiresDefault "access plus 2 days"
</IfModule>

# Set default index files
DirectoryIndex index.html index.php

# Custom error pages
ErrorDocument 400 /error.php?code=400
ErrorDocument 401 /error.php?code=401
ErrorDocument 403 /error.php?code=403
ErrorDocument 404 /error.php?code=404
ErrorDocument 500 /error.php?code=500
ErrorDocument 503 /error.php?code=503
