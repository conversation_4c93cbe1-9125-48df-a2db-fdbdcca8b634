<?php
class BaseController {
    protected $db;
    protected $user_data;
    
    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
        
        // Get user data from token if available
        $headers = getallheaders();
        if (isset($headers['Authorization'])) {
            $token = str_replace('Bearer ', '', $headers['Authorization']);
            $jwt = new JwtHandler();
            try {
                $this->user_data = $jwt->validateToken($token);
            } catch (Exception $e) {
                // If token validation fails, user_data will be null
                $this->user_data = null;
            }
        }
    }
    
    protected function sendResponse($status_code, $data) {
        http_response_code($status_code);
        echo json_encode($data);
        exit();
    }
    
    protected function validateRequiredFields($data, $required_fields) {
        $missing_fields = [];
        
        foreach ($required_fields as $field) {
            if (!isset($data[$field]) || empty($data[$field])) {
                $missing_fields[] = $field;
            }
        }
        
        if (!empty($missing_fields)) {
            $this->sendResponse(400, [
                'status' => 'error',
                'message' => 'Missing required fields',
                'missing_fields' => $missing_fields
            ]);
        }
        
        return true;
    }
    
    protected function logActivity($action) {
        if (!$this->user_data) {
            return false;
        }
        
        $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
        
        $query = "INSERT INTO audit_trail (user_id, action, table_name, record_id, ip_address, user_agent)
                 VALUES (:user_id, :action, :table_name, :record_id, :ip_address, :user_agent)";
        
        $stmt = $this->db->prepare($query);
        
        $stmt->bindParam(':user_id', $this->user_data['id']);
        $stmt->bindParam(':action', $action);
        $stmt->bindValue(':table_name', $this->table_name ?? 'unknown');
        $stmt->bindValue(':record_id', null);
        $stmt->bindParam(':ip_address', $ip_address);
        $stmt->bindParam(':user_agent', $user_agent);
        
        return $stmt->execute();
    }
}
