// Storage utility
const storage = {
    set: (key, value) => {
        try {
            if (typeof value === 'string') {
                localStorage.setItem(key, value);
            } else {
                localStorage.setItem(key, JSON.stringify(value));
            }
        } catch (e) {
            console.error('Storage set error:', e);
        }
    },
    get: (key) => {
        try {
            const item = localStorage.getItem(key);
            if (!item) return null;

            // Try to parse as JSON, if it fails return as string
            try {
                return JSON.parse(item);
            } catch {
                return item;
            }
        } catch (e) {
            console.error('Storage get error:', e);
            return null;
        }
    },
    remove: (key) => {
        try {
            localStorage.removeItem(key);
        } catch (e) {
            console.error('Storage remove error:', e);
        }
    }
};

// Simple API service for login
class LoginApiService {
    constructor() {
        this.baseUrl = 'api';
    }

    async login(credentials) {
        try {
            const response = await fetch(`${this.baseUrl}/auth/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                body: JSON.stringify(credentials)
            });

            const data = await response.json();
            
            if (!response.ok) {
                throw new Error(data.message || 'Login failed');
            }

            // Store token and user data
            if (data.data && data.data.token) {
                storage.set('mfs_auth_token', data.data.token);
                storage.set('mfs_user_data', data.data.user);
            }

            return data;
        } catch (error) {
            console.error('Login error:', error);
            throw error;
        }
    }
}

// Initialize API service
const apiService = new LoginApiService();

// Show alert function
function showAlert(message, type = 'danger') {
    const alertContainer = document.getElementById('alertContainer');
    const alertHtml = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    alertContainer.innerHTML = alertHtml;
}

// Clear alerts
function clearAlerts() {
    document.getElementById('alertContainer').innerHTML = '';
}

// Handle login form submission
document.addEventListener('DOMContentLoaded', function() {
    const loginForm = document.getElementById('loginForm');
    const submitButton = loginForm.querySelector('button[type="submit"]');
    
    // Check if already logged in
    const token = storage.get('mfs_auth_token');
    if (token) {
        window.location.href = 'dashboard.html';
        return;
    }
    
    loginForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        
        clearAlerts();
        
        // Get form data
        const username = document.getElementById('username').value.trim();
        const password = document.getElementById('password').value;
        
        // Basic validation
        if (!username || !password) {
            showAlert('Please enter both username and password.');
            return;
        }
        
        // Disable submit button
        const originalText = submitButton.innerHTML;
        submitButton.disabled = true;
        submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Signing In...';
        
        try {
            console.log('Attempting login with:', { username, password: '***' });

            const response = await apiService.login({
                username: username,
                password: password
            });

            console.log('Login response:', response);

            showAlert('Login successful! Redirecting...', 'success');

            // Verify token was stored
            const storedToken = storage.get('mfs_auth_token');
            const storedUser = storage.get('mfs_user_data');
            console.log('Stored token:', storedToken ? 'YES' : 'NO');
            console.log('Stored user:', storedUser ? 'YES' : 'NO');

            // Redirect after short delay
            setTimeout(() => {
                console.log('Redirecting to dashboard...');
                window.location.href = 'dashboard.html';
            }, 1500);

        } catch (error) {
            console.error('Login error:', error);
            showAlert(error.message || 'Login failed. Please try again.');
        } finally {
            // Re-enable submit button
            submitButton.disabled = false;
            submitButton.innerHTML = originalText;
        }
    });
    
    // Handle "Remember me" functionality
    const rememberMe = document.getElementById('rememberMe');
    const usernameField = document.getElementById('username');
    
    // Load saved username if available
    const savedUsername = localStorage.getItem('remembered_username');
    if (savedUsername) {
        usernameField.value = savedUsername;
        rememberMe.checked = true;
    }
    
    // Save/remove username based on checkbox
    rememberMe.addEventListener('change', function() {
        if (this.checked) {
            localStorage.setItem('remembered_username', usernameField.value);
        } else {
            localStorage.removeItem('remembered_username');
        }
    });
    
    // Update saved username when typing
    usernameField.addEventListener('input', function() {
        if (rememberMe.checked) {
            localStorage.setItem('remembered_username', this.value);
        }
    });
});
