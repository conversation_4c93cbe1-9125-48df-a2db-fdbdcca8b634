class ApiService {
    constructor() {
        this.baseUrl = API_CONFIG.BASE_URL;
        this.token = storage.get(APP_CONFIG.TOKEN_KEY);
    }

    // Set authentication token
    setToken(token) {
        this.token = token;
        storage.set(APP_CONFIG.TOKEN_KEY, token);
    }

    // Clear authentication token
    clearToken() {
        this.token = null;
        storage.remove(APP_CONFIG.TOKEN_KEY);
    }

    // Get request headers
    getHeaders(includeAuth = true) {
        const headers = {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        };

        if (includeAuth && this.token) {
            headers['Authorization'] = `Bearer ${this.token}`;
        }

        return headers;
    }

    // Handle API response
    async handleResponse(response) {
        const data = await response.json();
        
        if (!response.ok) {
            const error = new Error(data.message || 'Something went wrong');
            error.status = response.status;
            error.data = data;
            throw error;
        }

        return data;
    }

    // Auth API
    async login(credentials) {
        const response = await fetch(`${this.baseUrl}/auth/login`, {
            method: 'POST',
            headers: this.getHeaders(false),
            body: JSON.stringify(credentials)
        });

        const data = await this.handleResponse(response);
        if (data.token) {
            this.setToken(data.token);
            storage.set(APP_CONFIG.USER_KEY, data.user);
        }
        
        return data;
    }

    async register(userData) {
        const response = await fetch(`${this.baseUrl}/auth/register`, {
            method: 'POST',
            headers: this.getHeaders(false),
            body: JSON.stringify(userData)
        });

        const data = await this.handleResponse(response);
        if (data.token) {
            this.setToken(data.token);
            storage.set(APP_CONFIG.USER_KEY, data.user);
        }
        
        return data;
    }

    async getCurrentUser() {
        const response = await fetch(`${this.baseUrl}/auth/me`, {
            headers: this.getHeaders()
        });
        
        return this.handleResponse(response);
    }

    // Client API
    async getClients(params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const response = await fetch(`${this.baseUrl}/clients?${queryString}`, {
            headers: this.getHeaders()
        });
        
        return this.handleResponse(response);
    }

    async getClient(id) {
        const response = await fetch(`${this.baseUrl}/clients/${id}`, {
            headers: this.getHeaders()
        });
        
        return this.handleResponse(response);
    }

    async createClient(clientData) {
        const response = await fetch(`${this.baseUrl}/clients`, {
            method: 'POST',
            headers: this.getHeaders(),
            body: JSON.stringify(clientData)
        });
        
        return this.handleResponse(response);
    }

    async updateClient(id, clientData) {
        const response = await fetch(`${this.baseUrl}/clients/${id}`, {
            method: 'PUT',
            headers: this.getHeaders(),
            body: JSON.stringify(clientData)
        });
        
        return this.handleResponse(response);
    }

    // Loan API
    async getLoans(params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const response = await fetch(`${this.baseUrl}/loans?${queryString}`, {
            headers: this.getHeaders()
        });
        
        return this.handleResponse(response);
    }

    async getLoan(id) {
        const response = await fetch(`${this.baseUrl}/loans/${id}`, {
            headers: this.getHeaders()
        });
        
        return this.handleResponse(response);
    }

    async createLoan(loanData) {
        const response = await fetch(`${this.baseUrl}/loans`, {
            method: 'POST',
            headers: this.getHeaders(),
            body: JSON.stringify(loanData)
        });
        
        return this.handleResponse(response);
    }

    async updateLoanStatus(id, status) {
        const response = await fetch(`${this.baseUrl}/loans/${id}/status`, {
            method: 'PUT',
            headers: this.getHeaders(),
            body: JSON.stringify({ status })
        });
        
        return this.handleResponse(response);
    }

    // Payment API
    async recordPayment(paymentData) {
        const response = await fetch(`${this.baseUrl}/payments`, {
            method: 'POST',
            headers: this.getHeaders(),
            body: JSON.stringify(paymentData)
        });
        
        return this.handleResponse(response);
    }

    async getLoanPayments(loanId) {
        const response = await fetch(`${this.baseUrl}/loans/${loanId}/payments`, {
            headers: this.getHeaders()
        });
        
        return this.handleResponse(response);
    }

    // Dashboard API
    async getDashboardStats() {
        const response = await fetch(`${this.baseUrl}/dashboard/stats`, {
            headers: this.getHeaders()
        });
        
        return this.handleResponse(response);
    }

    // Report API
    async generateReport(type, params = {}) {
        const queryString = new URLSearchParams(params).toString();
        const response = await fetch(`${this.baseUrl}/reports/${type}?${queryString}`, {
            headers: this.getHeaders()
        });
        
        return this.handleResponse(response);
    }

    // File upload
    async uploadFile(file, endpoint) {
        const formData = new FormData();
        formData.append('file', file);

        const response = await fetch(`${this.baseUrl}${endpoint}`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${this.token}`
            },
            body: formData
        });
        
        return this.handleResponse(response);
    }
}

// Create a singleton instance
const apiService = new ApiService();

export default apiService;
